<?xml version="1.0" encoding="UTF-8"?>
<!--
  Maven项目对象模型(POM)配置文件
  该文件定义了Hyperledger Fabric智能合约项目的构建配置和依赖关系
-->
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- POM模型版本 -->
    <modelVersion>4.0.0</modelVersion>

    <!-- 项目坐标信息 -->
    <!-- 组织ID -->
    <groupId>com.simnectz.blockchain</groupId>
    <!-- 项目ID -->
    <artifactId>sample-chaincode</artifactId>
    <!-- 版本号，SNAPSHOT表示开发版本 -->
    <version>1.0.0-SNAPSHOT</version>
    <!-- 打包方式为jar包 -->
    <packaging>jar</packaging>

    <!-- 项目属性配置，定义各种版本号和编码设置 -->
    <properties>
        <!-- Java版本 -->
        <java.version>1.8</java.version>
        <!-- 源代码编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- 报告输出编码 -->
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <!-- 依赖库版本定义 -->
        <!-- Hyperledger Fabric版本 -->
        <fabric.version>2.4.0</fabric.version>
        <!-- Logback日志框架版本 -->
        <logback.version>1.2.11</logback.version>
        <!-- Lombok版本，用于简化Java代码 -->
        <lombok.version>1.18.22</lombok.version>
        <!-- FastJSON版本，用于JSON处理 -->
        <fastjson.version>1.2.75</fastjson.version>
        <!-- Protocol Buffers版本，用于序列化结构化数据 -->
        <protobuf-java.version>3.11.1</protobuf-java.version>
    </properties>

    <!-- 仓库配置，指定从哪些仓库下载依赖 -->
    <repositories>
        <!-- 阿里云Maven仓库，提供更快的依赖下载速度 -->
        <repository>
            <id>ali</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <!-- JitPack仓库，用于获取GitHub上的项目 -->
        <repository>
            <id>jitpack.io</id>
            <url>https://www.jitpack.io</url>
        </repository>
        <!-- Hyperledger Fabric官方Maven仓库 -->
        <repository>
            <id>artifactory</id>
            <url>https://hyperledger.jfrog.io/hyperledger/fabric-maven</url>
        </repository>
    </repositories>

    <!-- 项目依赖配置 -->
    <dependencies>
        <!-- Fabric链码开发核心依赖 -->
        <dependency>
            <groupId>org.hyperledger.fabric-chaincode-java</groupId>
            <artifactId>fabric-chaincode-shim</artifactId>
            <version>${fabric.version}</version>
        </dependency>
        <!-- Fabric协议缓冲区定义 -->
        <dependency>
            <groupId>org.hyperledger.fabric-chaincode-java</groupId>
            <artifactId>fabric-chaincode-protos</artifactId>
            <version>${fabric.version}</version>
        </dependency>
        <!-- Logback日志框架 - 经典实现 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <!-- Logback日志框架 - 核心模块 -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
            <version>${logback.version}</version>
        </dependency>
        <!-- Lombok，用于减少样板代码 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <!-- FastJSON，用于JSON序列化和反序列化 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <!-- Protocol Buffers Java实现 -->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>${protobuf-java.version}</version>
        </dependency>

        <!-- 测试依赖 -->
        <!-- JUnit 5 测试框架 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.8.2</version>
            <scope>test</scope>
        </dependency>
        <!-- Mockito 模拟框架 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>
        <!-- Mockito JUnit 5 扩展 -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.6.1</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <!-- 构建配置 -->
    <build>
        <!-- 指定源代码目录 -->
        <sourceDirectory>src/main/java</sourceDirectory>
        <!-- 构建插件配置 -->
        <plugins>
            <!-- Maven编译插件，控制Java编译选项 -->
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <!-- 指定源代码和目标Java版本 -->
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <!-- Maven Shade插件，用于创建包含所有依赖的可执行JAR包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <!-- 在package阶段执行 -->
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <!-- 最终生成的JAR包名称为chaincode.jar -->
                            <finalName>chaincode</finalName>
                            <!-- 资源转换器配置 -->
                            <transformers>
                                <!-- 配置主类，使JAR包可执行 -->
                                <transformer
                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <!-- 指定Fabric合约路由器作为主类 -->
                                    <mainClass>org.hyperledger.fabric.contract.ContractRouter</mainClass>
                                </transformer>
                            </transformers>
                            <!-- 过滤器配置 -->
                            <filters>
                                <filter>
                                    <!-- 过滤掉签名文件，避免打包时的安全异常 -->
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>module-info.class</exclude>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>