package com.simnectz.blockchain.sample.contract;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.simnectz.blockchain.sample.entity.AccountEntity;
import com.simnectz.blockchain.sample.entity.ExchangeRateEntity;
import com.simnectz.blockchain.sample.entity.TransactionRecord;
import com.simnectz.blockchain.sample.entity.UserEntity;
import com.simnectz.blockchain.sample.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.hyperledger.fabric.contract.Context;
import org.hyperledger.fabric.contract.ContractInterface;
import org.hyperledger.fabric.contract.annotation.Contact;
import org.hyperledger.fabric.contract.annotation.Contract;
import org.hyperledger.fabric.contract.annotation.Default;
import org.hyperledger.fabric.contract.annotation.Info;
import org.hyperledger.fabric.contract.annotation.License;
import org.hyperledger.fabric.contract.annotation.Transaction;
import org.hyperledger.fabric.shim.ChaincodeException;
import org.hyperledger.fabric.shim.ChaincodeStub;
import org.hyperledger.fabric.shim.ledger.KeyModification;
import org.hyperledger.fabric.shim.ledger.KeyValue;
import org.hyperledger.fabric.shim.ledger.QueryResultsIterator;
import org.hyperledger.fabric.shim.ledger.QueryResultsIteratorWithMetadata;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账户合约
 * <p>
 * 该合约实现了账户的CRUD操作、分页查询、根据ID查询详情和查询账本历史记录的功能
 * 同时提供了本地货币汇率表的生成、查询、更新和删除功能
 * 并提供了一个综合的汇率表增删改查方法
 * 整合了用户管理的功能，提供用户的CRUD操作、分页查询和历史记录查询功能
 *
 * <AUTHOR>
 * @version 1.0
 */
@Contract(
        name = "AccountContract",
        info = @Info(
                title = "账户合约",
                description = "账户管理的智能合约，提供CRUD、分页查询、历史记录查询和汇率表的增删改查功能，同时整合了用户管理功能",
                version = "1.0",
                license = @License(
                        name = "Apache 2.0 License",
                        url = "https://www.apache.org/licenses/LICENSE-2.0.html"),
                contact = @Contact(
                        email = "<EMAIL>",
                        name = "AccountContract",
                        url = "https://hyperledger.example.com")
        )
)
@Default
@Slf4j
public class AccountContract implements ContractInterface {

    // 账户对象在账本中的前缀
    private static final String ACCOUNT_PREFIX = "ACCOUNT_";

    // 汇率对象在账本中的前缀
    private static final String EXCHANGE_RATE_PREFIX = "EXCHANGE_RATE_";

    // 用户对象在账本中的前缀
    private static final String USER_PREFIX = "USER_";

    // 成功状态码
    private static final Integer SUCCESS_CODE = 20000;

    @Transaction(name = "Init", intent = Transaction.TYPE.SUBMIT)
    public String init(final Context ctx) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();
        return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "合约初始化成功", txTimestamp, stub.getTxId()));
    }

    /**
     * 创建账户
     *
     * @param ctx 交易上下文
     * @param account 账户信息
     * @return 创建结果
     */
    @Transaction(name = "CreateAccount", intent = Transaction.TYPE.SUBMIT)
    public String createAccount(final Context ctx, final AccountEntity account) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 检查账户ID是否已存在
            String accountKey = ACCOUNT_PREFIX + account.getAccountId();
            String existingAccountJson = stub.getStringState(accountKey);
            if (existingAccountJson != null && !existingAccountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 已存在", account.getAccountId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 设置创建时间戳
            account.setCreatedTimestamp(txTimestamp);

            // 将账户对象序列化为JSON并存储到账本
            stub.putStringState(accountKey, JSON.toJSONString(account));

            stub.setEvent("create_account_event", JSON.toJSONString(account).getBytes(StandardCharsets.UTF_8));

            log.info("账户创建成功: {}", account.getAccountId());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "账户创建成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("创建账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 根据ID查询账户详情
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @return 账户详情
     */
    @Transaction(name = "GetAccount", intent = Transaction.TYPE.EVALUATE)
    public String getAccount(final Context ctx, final String accountId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建账户键
            String accountKey = ACCOUNT_PREFIX + accountId;

            // 从账本获取账户数据
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在", accountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析账户数据
            AccountEntity account = JSON.parseObject(accountJson, AccountEntity.class);

            log.info("查询账户成功: {}", accountId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询账户成功", account, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 更新账户信息
     *
     * @param ctx 交易上下文
     * @param account 账户信息
     * @return 更新结果
     */
    @Transaction(name = "UpdateAccount", intent = Transaction.TYPE.SUBMIT)
    public String updateAccount(final Context ctx, final AccountEntity account) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 检查账户ID是否存在
            String accountKey = ACCOUNT_PREFIX + account.getAccountId();
            String existingAccountJson = stub.getStringState(accountKey);
            if (existingAccountJson == null || existingAccountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在，无法更新", account.getAccountId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析现有账户数据
            AccountEntity existingAccount = JSON.parseObject(existingAccountJson, AccountEntity.class);

            // 保留创建时间戳
            account.setCreatedTimestamp(existingAccount.getCreatedTimestamp());

            // 将更新后的账户对象序列化为JSON并存储到账本
            stub.putStringState(accountKey, JSON.toJSONString(account));

            log.info("账户更新成功: {}", account.getAccountId());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "账户更新成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("更新账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 删除账户（逻辑删除）
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @return 删除结果
     */
    @Transaction(name = "DeleteAccount", intent = Transaction.TYPE.SUBMIT)
    public String deleteAccount(final Context ctx, final String accountId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建账户键
            String accountKey = ACCOUNT_PREFIX + accountId;

            // 从账本获取账户数据
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在，无法删除", accountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析账户数据
            AccountEntity account = JSON.parseObject(accountJson, AccountEntity.class);

            // 逻辑删除：将状态设置为false
            account.setStatus(false);

            // 将更新后的账户对象序列化为JSON并存储到账本
            stub.putStringState(accountKey, JSON.toJSONString(account));

            log.info("账户删除成功: {}", accountId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "账户删除成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("删除账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 根据条件分页查询账户
     *
     * @param ctx 交易上下文
     * @param queryString 查询条件JSON字符串
     * @param pageSize 每页记录数
     * @param bookmark 分页书签
     * @return 查询结果
     */
    @Transaction(name = "QueryAccounts", intent = Transaction.TYPE.EVALUATE)
    public String queryAccounts(final Context ctx, final String queryString, final int pageSize, final String bookmark) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 解析查询条件
            JSONObject queryJson = JSON.parseObject(queryString);

            // 构建CouchDB查询语句
            StringBuilder queryBuilder = new StringBuilder("{\"selector\":{");

            // 添加前缀条件，确保只查询账户对象
            queryBuilder.append("\"_id\":{\"$regex\":\"^").append(ACCOUNT_PREFIX).append("\"}");

            // 添加其他查询条件
            if (queryJson != null && !queryJson.isEmpty()) {
                for (Map.Entry<String, Object> entry : queryJson.entrySet()) {
                    queryBuilder.append(",\"").append(entry.getKey()).append("\":");

                    // 根据值类型构建不同的查询条件
                    if (entry.getValue() instanceof String) {
                        queryBuilder.append("\"").append(entry.getValue()).append("\"");
                    } else {
                        queryBuilder.append(entry.getValue());
                    }
                }
            }

            queryBuilder.append("}}");

            // 执行分页查询
            QueryResultsIteratorWithMetadata<KeyValue> queryResults = stub.getQueryResultWithPagination(queryBuilder.toString(), pageSize, bookmark);

            // 处理查询结果
            List<AccountEntity> accounts = new ArrayList<>();
            for (KeyValue kv : queryResults) {
                AccountEntity account = JSON.parseObject(kv.getStringValue(), AccountEntity.class);
                accounts.add(account);
            }

            // 构建分页结果
            Map<String, Object> paginationResult = new HashMap<>();
            paginationResult.put("records", accounts);
            paginationResult.put("recordCount", accounts.size());
            paginationResult.put("bookmark", queryResults.getMetadata().getBookmark());
            paginationResult.put("fetchedRecordsCount", queryResults.getMetadata().getFetchedRecordsCount());

            log.info("查询账户成功，返回记录数: {}", accounts.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询账户成功", paginationResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询账户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 获取账户的历史记录
     *
     * @param ctx 交易上下文
     * @param accountId 账户ID
     * @return 历史记录
     */
    @Transaction(name = "GetAccountHistory", intent = Transaction.TYPE.EVALUATE)
    public String getAccountHistory(final Context ctx, final String accountId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建账户键
            String accountKey = ACCOUNT_PREFIX + accountId;

            // 检查账户是否存在
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("账户ID %s 不存在，无法查询历史记录", accountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取历史记录
            QueryResultsIterator<KeyModification> historyIterator = stub.getHistoryForKey(accountKey);

            // 处理历史记录
            List<Map<String, Object>> historyList = new ArrayList<>();
            for (KeyModification modification : historyIterator) {
                Map<String, Object> historyItem = new HashMap<>();
                historyItem.put("txId", modification.getTxId());
                historyItem.put("timestamp", modification.getTimestamp().toEpochMilli());
                historyItem.put("isDelete", modification.isDeleted());

                // 解析账户数据
                if (!modification.isDeleted() && modification.getValue() != null) {
                    AccountEntity account = JSON.parseObject(modification.getStringValue(), AccountEntity.class);
                    historyItem.put("value", account);
                }

                historyList.add(historyItem);
            }

            log.info("查询账户历史记录成功: {}, 记录数: {}", accountId, historyList.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询账户历史记录成功", historyList, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询账户历史记录失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }


    /**
     * 创建汇率表
     *
     * @param ctx 交易上下文
     * @param exchangeRateJson 汇率信息
     * @return 创建结果
     */
    @Transaction(name = "CreateExchangeRate", intent = Transaction.TYPE.SUBMIT)
    public String createExchangeRate(final Context ctx, final String exchangeRateJson) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            ExchangeRateEntity exchangeRate = JSON.parseObject(exchangeRateJson, ExchangeRateEntity.class);

            String rateKey = EXCHANGE_RATE_PREFIX + exchangeRate.getRateId();
            String existingRateJson = stub.getStringState(rateKey);
            if (existingRateJson != null && !existingRateJson.isEmpty()) {
                String errorMessage = String.format("汇率ID %s 已存在", exchangeRate.getRateId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            exchangeRate.setStatus(true);
            exchangeRate.setCreatedTimestamp(txTimestamp);
            exchangeRate.setUpdatedTimestamp(txTimestamp);

            stub.putStringState(rateKey, JSON.toJSONString(exchangeRate));

            log.info("汇率表创建成功: {}", exchangeRate.getRateId());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "汇率表创建成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("创建汇率表失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 查询汇率表
     *
     * @param ctx 交易上下文
     * @param rateId 汇率ID
     * @return 汇率详情
     */
    @Transaction(name = "GetExchangeRate", intent = Transaction.TYPE.EVALUATE)
    public String getExchangeRate(final Context ctx, final String rateId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建汇率键
            String rateKey = EXCHANGE_RATE_PREFIX + rateId;

            // 从账本获取汇率数据
            String rateJson = stub.getStringState(rateKey);
            if (rateJson == null || rateJson.isEmpty()) {
                String errorMessage = String.format("汇率ID %s 不存在", rateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析汇率数据
            ExchangeRateEntity exchangeRate = JSON.parseObject(rateJson, ExchangeRateEntity.class);

            log.info("查询汇率成功: {}", rateId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询汇率成功", exchangeRate, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询汇率失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 更新汇率表
     *
     * @param ctx 交易上下文
     * @param exchangeRateJson 汇率信息
     * @return 更新结果
     */
    @Transaction(name = "UpdateExchangeRate", intent = Transaction.TYPE.SUBMIT)
    public String updateExchangeRate(final Context ctx, final  String exchangeRateJson) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            ExchangeRateEntity exchangeRate = JSON.parseObject(exchangeRateJson, ExchangeRateEntity.class);
            // 检查汇率ID是否存在
            String rateKey = EXCHANGE_RATE_PREFIX + exchangeRate.getRateId();
            String existingRateJson = stub.getStringState(rateKey);
            if (existingRateJson == null || existingRateJson.isEmpty()) {
                String errorMessage = String.format("汇率ID %s 不存在，无法更新", exchangeRate.getRateId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析现有汇率数据
            ExchangeRateEntity existingRate = JSON.parseObject(existingRateJson, ExchangeRateEntity.class);

            // 保留原有的一些属性
            exchangeRate.setCreatedTimestamp(existingRate.getCreatedTimestamp());
            exchangeRate.setUpdatedTimestamp(txTimestamp);

            // 确保状态为有效
            exchangeRate.setStatus(true);

            // 将更新后的汇率对象序列化为JSON并存储到账本
            stub.putStringState(rateKey, JSON.toJSONString(exchangeRate));

            log.info("汇率表更新成功: {}", exchangeRate.getRateId());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "汇率表更新成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("更新汇率表失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 删除汇率表（逻辑删除）
     *
     * @param ctx 交易上下文
     * @param rateId 汇率ID
     * @return 删除结果
     */
    @Transaction(name = "DeleteExchangeRate", intent = Transaction.TYPE.SUBMIT)
    public String deleteExchangeRate(final Context ctx, final String rateId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建汇率键
            String rateKey = EXCHANGE_RATE_PREFIX + rateId;

            // 从账本获取汇率数据
            String rateJson = stub.getStringState(rateKey);
            if (rateJson == null || rateJson.isEmpty()) {
                String errorMessage = String.format("汇率ID %s 不存在，无法删除", rateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析汇率数据
            ExchangeRateEntity exchangeRate = JSON.parseObject(rateJson, ExchangeRateEntity.class);

            // 逻辑删除：将状态设置为false
            exchangeRate.setStatus(false);
            exchangeRate.setUpdatedTimestamp(txTimestamp);

            // 将更新后的汇率对象序列化为JSON并存储到账本
            stub.putStringState(rateKey, JSON.toJSONString(exchangeRate));

            log.info("汇率表删除成功: {}", rateId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "汇率表删除成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("删除汇率表失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }


    /**
     * 银行创建或更新本国货币汇率表
     *
     * @param ctx 交易上下文
     * @param bankAccountId 银行账户ID
     * @param exchangeRate 汇率信息
     * @return 操作结果
     */
    @Transaction(name = "BankManageCountryExchangeRate", intent = Transaction.TYPE.SUBMIT)
    public String bankManageCountryExchangeRate(final Context ctx, final String bankAccountId, final ExchangeRateEntity exchangeRate) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 获取银行账户信息
            String accountKey = ACCOUNT_PREFIX + bankAccountId;
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("银行账户ID %s 不存在", bankAccountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析银行账户数据
            AccountEntity bankAccount = JSON.parseObject(accountJson, AccountEntity.class);

            // 获取银行所在国家
            String country = bankAccount.getCountry();
            if (country == null || country.isEmpty()) {
                String errorMessage = String.format("银行账户 %s 未设置所在国家信息", bankAccountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 构建汇率键
            String rateKey = EXCHANGE_RATE_PREFIX + exchangeRate.getRateId();
            String existingRateJson = stub.getStringState(rateKey);
            boolean isUpdate = existingRateJson != null && !existingRateJson.isEmpty();

            // 设置国家信息
            exchangeRate.setCountry(country); // 设置为银行所在国家

            // 设置货币信息

            // 设置状态为有效
            exchangeRate.setStatus(true);

            if (isUpdate) {
                // 如果是更新，保留创建时间戳
                ExchangeRateEntity existingRate = JSON.parseObject(existingRateJson, ExchangeRateEntity.class);
                exchangeRate.setCreatedTimestamp(existingRate.getCreatedTimestamp());

                // 检查是否为同一国家的汇率
                if (!country.equals(existingRate.getCountry())) {
                    String errorMessage = String.format("汇率ID %s 不属于银行所在国家 %s，无法更新", exchangeRate.getRateId(), country);
                    log.error(errorMessage);
                    throw new ChaincodeException(errorMessage);
                }
            } else {
                // 如果是新建，设置创建时间戳
                exchangeRate.setCreatedTimestamp(txTimestamp);
            }

            exchangeRate.setUpdatedTimestamp(txTimestamp);

            // 将汇率对象序列化为JSON并存储到账本
            stub.putStringState(rateKey, JSON.toJSONString(exchangeRate));

            String resultMessage = isUpdate ? "汇率表更新成功" : "汇率表创建成功";
            log.info("{}: {}, 国家: {}", resultMessage, exchangeRate.getRateId(), country);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, resultMessage, txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("银行管理本国汇率表失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 银行查询本国所有货币汇率表
     *
     * @param ctx 交易上下文
     * @param bankAccountId 银行账户ID
     * @return 汇率表列表
     */
    @Transaction(name = "QueryBankCountryExchangeRates", intent = Transaction.TYPE.EVALUATE)
    public String queryBankCountryExchangeRates(final Context ctx, final String bankAccountId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 获取银行账户信息
            String accountKey = ACCOUNT_PREFIX + bankAccountId;
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("银行账户ID %s 不存在", bankAccountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析银行账户数据
            AccountEntity bankAccount = JSON.parseObject(accountJson, AccountEntity.class);

            // 获取银行所在国家
            String country = bankAccount.getCountry();
            if (country == null || country.isEmpty()) {
                String errorMessage = String.format("银行账户 %s 未设置所在国家信息", bankAccountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 构建CouchDB查询语句，查询指定国家的所有汇率表
            String queryString = String.format("{\"selector\":{\"_id\":{\"$regex\":\"^%s\"},\"country\":\"%s\",\"status\":true}}",
                    EXCHANGE_RATE_PREFIX, country);

            // 执行查询
            QueryResultsIterator<KeyValue> queryResults = stub.getQueryResult(queryString);

            // 处理查询结果
            List<ExchangeRateEntity> exchangeRates = new ArrayList<>();
            for (KeyValue kv : queryResults) {
                ExchangeRateEntity rate = JSON.parseObject(kv.getStringValue(), ExchangeRateEntity.class);
                exchangeRates.add(rate);
            }

            log.info("查询银行 {} 所在国家 {} 的汇率表成功，返回记录数: {}", bankAccountId, country, exchangeRates.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询本国汇率表成功", exchangeRates, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询银行本国汇率表失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 银行删除本国特定货币汇率表
     *
     * @param ctx 交易上下文
     * @param bankAccountId 银行账户ID
     * @param rateId 汇率ID
     * @return 删除结果
     */
    @Transaction(name = "DeleteBankCountryExchangeRate", intent = Transaction.TYPE.SUBMIT)
    public String deleteBankCountryExchangeRate(final Context ctx, final String bankAccountId, final String rateId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 获取银行账户信息
            String accountKey = ACCOUNT_PREFIX + bankAccountId;
            String accountJson = stub.getStringState(accountKey);
            if (accountJson == null || accountJson.isEmpty()) {
                String errorMessage = String.format("银行账户ID %s 不存在", bankAccountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析银行账户数据
            AccountEntity bankAccount = JSON.parseObject(accountJson, AccountEntity.class);

            // 获取银行所在国家
            String country = bankAccount.getCountry();
            if (country == null || country.isEmpty()) {
                String errorMessage = String.format("银行账户 %s 未设置所在国家信息", bankAccountId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 构建汇率键
            String rateKey = EXCHANGE_RATE_PREFIX + rateId;

            // 从账本获取汇率数据
            String rateJson = stub.getStringState(rateKey);
            if (rateJson == null || rateJson.isEmpty()) {
                String errorMessage = String.format("汇率ID %s 不存在，无法删除", rateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析汇率数据
            ExchangeRateEntity exchangeRate = JSON.parseObject(rateJson, ExchangeRateEntity.class);

            // 检查是否为同一国家的汇率
            if (!country.equals(exchangeRate.getCountry())) {
                String errorMessage = String.format("汇率ID %s 不属于银行所在国家 %s，无法删除", rateId, country);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 逻辑删除：将状态设置为false
            exchangeRate.setStatus(false);
            exchangeRate.setUpdatedTimestamp(txTimestamp);

            // 将更新后的汇率对象序列化为JSON并存储到账本
            stub.putStringState(rateKey, JSON.toJSONString(exchangeRate));

            log.info("银行 {} 删除本国汇率表成功: {}", bankAccountId, rateId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "删除本国汇率表成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("删除银行本国汇率表失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 管理汇率表（综合方法）
     * 根据操作类型执行不同的操作：create, update, delete, query, queryPage
     *
     * @param ctx 交易上下文
     * @param operation 操作类型
     * @param queryString 查询条件JSON字符串（仅在queryPage操作时使用）
     * @param pageSize 每页记录数（仅在queryPage操作时使用）
     * @param bookmark 分页书签（仅在queryPage操作时使用）
     * @return 操作结果
     */
    @Transaction(name = "ManageExchangeRate", intent = Transaction.TYPE.SUBMIT)
    public String manageExchangeRate(final Context ctx, final String operation, final String rateId,
                                  final String exchangeRateJson, final String queryString,
                                  final Integer pageSize, final String bookmark) {
        switch (operation.toLowerCase()) {
            case "create":
                return createExchangeRate(ctx, exchangeRateJson);
            case "update":
                return updateExchangeRate(ctx, exchangeRateJson);
            case "delete":
                return deleteExchangeRate(ctx, rateId);
            case "query":
                return getExchangeRate(ctx, rateId);
            case "querypage":
                // 调用分页查询方法
                int pageSizeValue = (pageSize != null && pageSize > 0) ? pageSize : 10; // 默认每页10条
                String bookmarkValue = (bookmark != null) ? bookmark : ""; // 默认空书签
                String queryJsonStr = (queryString != null && !queryString.isEmpty()) ? queryString : "{}";

                return queryExchangeRates(ctx, queryJsonStr, pageSizeValue, bookmarkValue);
            default:
                String errorMessage = String.format("不支持的操作类型: %s", operation);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 银行管理本国货币汇率表（综合方法）
     * 根据操作类型执行不同的操作：create, update, delete, query, queryPage
     *
     * @param ctx 交易上下文
     * @param operation 操作类型
     * @param bankAccountId 银行账户ID
     * @param exchangeRate 汇率信息
     * @param queryString 查询条件JSON字符串（仅在queryPage操作时使用）
     * @param pageSize 每页记录数（仅在queryPage操作时使用）
     * @param bookmark 分页书签（仅在queryPage操作时使用）
     * @return 操作结果
     */
    @Transaction(name = "ManageBankCountryExchangeRate", intent = Transaction.TYPE.SUBMIT)
    public String manageBankCountryExchangeRate(final Context ctx, final String operation, final String bankAccountId,
                                              final ExchangeRateEntity exchangeRate, final String queryString,
                                              final Integer pageSize, final String bookmark) {
        switch (operation.toLowerCase()) {
            case "create":
            case "update":
                return bankManageCountryExchangeRate(ctx, bankAccountId, exchangeRate);
            case "delete":
                return deleteBankCountryExchangeRate(ctx, bankAccountId, exchangeRate.getRateId());
            case "query":
                return queryBankCountryExchangeRates(ctx, bankAccountId);
            case "querypage":
                // 构建查询条件，添加国家筛选
                JSONObject queryJson = queryString != null && !queryString.isEmpty() ?
                                      JSON.parseObject(queryString) : new JSONObject();

                // 获取银行账户信息以确定国家
                ChaincodeStub stub = ctx.getStub();
                String accountKey = ACCOUNT_PREFIX + bankAccountId;
                String accountJson = stub.getStringState(accountKey);
                if (accountJson == null || accountJson.isEmpty()) {
                    String errorMessage = String.format("银行账户ID %s 不存在", bankAccountId);
                    log.error(errorMessage);
                    throw new ChaincodeException(errorMessage);
                }

                // 解析银行账户数据
                AccountEntity bankAccount = JSON.parseObject(accountJson, AccountEntity.class);

                // 获取银行所在国家
                String country = bankAccount.getCountry();
                if (country == null || country.isEmpty()) {
                    String errorMessage = String.format("银行账户 %s 未设置所在国家信息", bankAccountId);
                    log.error(errorMessage);
                    throw new ChaincodeException(errorMessage);
                }

                // 添加国家筛选条件
                queryJson.put("country", country);
                queryJson.put("status", true);

                // 调用分页查询方法
                int pageSizeValue = (pageSize != null && pageSize > 0) ? pageSize : 10; // 默认每页10条
                String bookmarkValue = (bookmark != null) ? bookmark : ""; // 默认空书签

                return queryExchangeRates(ctx, queryJson.toJSONString(), pageSizeValue, bookmarkValue);
            default:
                String errorMessage = String.format("不支持的操作类型: %s", operation);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 创建用户
     *
     * @param ctx 交易上下文
     * @param user 用户信息
     * @return 创建结果
     */
    @Transaction(name = "CreateUser", intent = Transaction.TYPE.SUBMIT)
    public String createUser(final Context ctx, final UserEntity user) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 检查用户ID是否已存在
            String userKey = USER_PREFIX + user.getUserId();
            String existingUserJson = stub.getStringState(userKey);
            if (existingUserJson != null && !existingUserJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 已存在", user.getUserId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 设置创建时间戳
            user.setCreatedTimestamp(txTimestamp);
            user.setStatus(true);

            // 确保币种和余额数组已初始化
            if (user.getCurrency() == null || user.getCurrency().length == 0) {
                // 默认设置一个币种（CNY）
                user.setCurrency(new String[]{"CNY"});
            }

            if (user.getBalance() == null || user.getBalance().length == 0 ||
                user.getBalance().length != user.getCurrency().length) {
                // 初始化余额数组，与币种数组长度一致
                Double[] balances = new Double[user.getCurrency().length];
                // 所有币种初始余额设置为0
                for (int i = 0; i < balances.length; i++) {
                    balances[i] = 0.0;
                }
                user.setBalance(balances);
            }

            // 确保账户类型已设置
            if (user.getAccountType() == null || user.getAccountType().isEmpty()) {
                // 默认设置为储蓄账户
                user.setAccountType("SAVING");
            }

            // 确保身份信息已设置
            if (user.getIdentity() == null || user.getIdentity().isEmpty()) {
                // 默认设置为空字符串
                user.setIdentity("");
            }

            // 将用户对象序列化为JSON并存储到账本
            String userJson = JSON.toJSONString(user);
            stub.putStringState(userKey, userJson);

            log.info("用户创建成功: {}", user.getUserId());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "用户创建成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("创建用户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 根据ID查询用户详情
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @return 用户详情
     */
    @Transaction(name = "GetUser", intent = Transaction.TYPE.EVALUATE)
    public String getUser(final Context ctx, final String userId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建用户键
            String userKey = USER_PREFIX + userId;

            // 从账本获取用户数据
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析用户数据，也就是反序列化成UserEntity类实体
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            log.info("查询用户成功: {}", userId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询用户成功", user, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询用户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 更新用户信息
     *
     * @param ctx 交易上下文
     * @param user 用户信息
     * @return 更新结果
     */
    @Transaction(name = "UpdateUser", intent = Transaction.TYPE.SUBMIT)
    public String updateUser(final Context ctx, final UserEntity user) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 检查用户ID是否存在
            String userKey = USER_PREFIX + user.getUserId();
            String existingUserJson = stub.getStringState(userKey);
            if (existingUserJson == null || existingUserJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在，无法更新", user.getUserId());
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析现有用户数据
            UserEntity existingUser = JSON.parseObject(existingUserJson, UserEntity.class);

            // 保留创建时间戳
            user.setCreatedTimestamp(existingUser.getCreatedTimestamp());

            // 验证并处理币种和余额数组
            if (user.getCurrency() == null || user.getCurrency().length == 0) {
                // 使用现有用户的币种数组
                user.setCurrency(existingUser.getCurrency());
                user.setBalance(existingUser.getBalance());
            } else if (user.getBalance() == null || user.getBalance().length == 0 ||
                       user.getBalance().length != user.getCurrency().length) {
                // 如果币种数组已更新但余额数组不匹配，初始化新的余额数组
                Double[] newBalances = new Double[user.getCurrency().length];

                // 尝试保留现有币种的余额
                for (int i = 0; i < user.getCurrency().length; i++) {
                    String currencyCode = user.getCurrency()[i];
                    boolean found = false;

                    // 查找现有用户中对应的币种和余额
                    for (int j = 0; j < existingUser.getCurrency().length; j++) {
                        if (currencyCode.equals(existingUser.getCurrency()[j])) {
                            newBalances[i] = existingUser.getBalance()[j];
                            found = true;
                            break;
                        }
                    }

                    // 如果是新增币种，设置初始余额为0
                    if (!found) {
                        newBalances[i] = 0.0;
                    }
                }

                user.setBalance(newBalances);
            }

            // 确保账户类型已设置
            if (user.getAccountType() == null || user.getAccountType().isEmpty()) {
                user.setAccountType(existingUser.getAccountType() != null ?
                                    existingUser.getAccountType() : "SAVING");
            }

            // 确保身份信息已设置
            if (user.getIdentity() == null) {
                user.setIdentity(existingUser.getIdentity());
            }

            // 将更新后的用户对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));

            log.info("用户更新成功: {}", user.getUserId());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "用户更新成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("更新用户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 删除用户（逻辑删除）
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @return 删除结果
     */
    @Transaction(name = "DeleteUser", intent = Transaction.TYPE.SUBMIT)
    public String deleteUser(final Context ctx, final String userId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建用户键
            String userKey = USER_PREFIX + userId;

            // 从账本获取用户数据
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在，无法删除", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析用户数据
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 逻辑删除：将状态设置为false
            user.setStatus(false);

            // 将更新后的用户对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));

            log.info("用户删除成功: {}", userId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "用户删除成功", txTimestamp, stub.getTxId()));
        } catch (Exception e) {
            String errorMessage = String.format("删除用户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 根据条件分页查询用户
     *
     * @param ctx 交易上下文
     * @param queryString 查询条件JSON字符串
     * @param pageSize 每页记录数
     * @param bookmark 分页书签
     * @return 查询结果
     */
    @Transaction(name = "QueryUsers", intent = Transaction.TYPE.EVALUATE)
    public String queryUsers(final Context ctx, final String queryString, final int pageSize, final String bookmark) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 解析查询条件
            JSONObject queryJson = JSON.parseObject(queryString);

            // 构建CouchDB查询语句
            StringBuilder queryBuilder = new StringBuilder("{\"selector\":{");

            // 添加前缀条件，确保只查询用户对象
            queryBuilder.append("\"_id\":{\"$regex\":\"^").append(USER_PREFIX).append("\"}");

            // 添加其他查询条件
            if (queryJson != null && !queryJson.isEmpty()) {
                for (Map.Entry<String, Object> entry : queryJson.entrySet()) {
                    queryBuilder.append(",\"").append(entry.getKey()).append("\":");

                    // 根据值类型构建不同的查询条件
                    if (entry.getValue() instanceof String) {
                        queryBuilder.append("\"").append(entry.getValue()).append("\"");
                    } else {
                        queryBuilder.append(entry.getValue());
                    }
                }
            }

            queryBuilder.append("}}");

            // 执行分页查询
            QueryResultsIteratorWithMetadata<KeyValue> queryResults = stub.getQueryResultWithPagination(queryBuilder.toString(), pageSize, bookmark);

            // 处理查询结果
            List<UserEntity> users = new ArrayList<>();
            for (KeyValue kv : queryResults) {
                UserEntity user = JSON.parseObject(kv.getStringValue(), UserEntity.class);
                users.add(user);
            }

            // 构建分页结果
            Map<String, Object> paginationResult = new HashMap<>();
            paginationResult.put("records", users);
            paginationResult.put("recordCount", users.size());
            paginationResult.put("bookmark", queryResults.getMetadata().getBookmark());
            paginationResult.put("fetchedRecordsCount", queryResults.getMetadata().getFetchedRecordsCount());

            log.info("查询用户成功，返回记录数: {}", users.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询用户成功", paginationResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询用户失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 获取用户的历史记录
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @return 历史记录
     */
    @Transaction(name = "GetUserHistory", intent = Transaction.TYPE.EVALUATE)
    public String getUserHistory(final Context ctx, final String userId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建用户键
            String userKey = USER_PREFIX + userId;

            // 检查用户是否存在
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在，无法查询历史记录", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取历史记录
            QueryResultsIterator<KeyModification> historyIterator = stub.getHistoryForKey(userKey);

            // 处理历史记录
            List<Map<String, Object>> historyList = new ArrayList<>();
            for (KeyModification modification : historyIterator) {
                Map<String, Object> historyItem = new HashMap<>();
                historyItem.put("txId", modification.getTxId());
                historyItem.put("timestamp", modification.getTimestamp().toEpochMilli());
                historyItem.put("isDelete", modification.isDeleted());

                // 解析用户数据
                if (!modification.isDeleted() && modification.getValue() != null) {
                    UserEntity user = JSON.parseObject(modification.getStringValue(), UserEntity.class);
                    historyItem.put("value", user);
                }

                historyList.add(historyItem);
            }

            log.info("查询用户历史记录成功: {}, 记录数: {}", userId, historyList.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询用户历史记录成功", historyList, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询用户历史记录失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }


    /**
     * 带初始余额的用户注册功能
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param password 用户密码
     * @param initialBalance 初始余额
     * @return 注册结果
     */
    @Transaction(name = "RegisterUserWithBalance", intent = Transaction.TYPE.SUBMIT)
    public String registerUserWithBalance(final Context ctx, final String userId, final String password, final double initialBalance) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 检查用户ID是否已存在
            String userKey = USER_PREFIX + userId;
            String existingUserJson = stub.getStringState(userKey);
            if (existingUserJson != null && !existingUserJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 已存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成随机16位卡号
            String cardId = generateCardId(userId, txTimestamp);

            // 创建用户实体
            UserEntity user = new UserEntity();
            user.setUserId(userId);
            user.setPassword(password);
            user.setCardId(cardId);
            user.setStatus(true);
            user.setCreatedTimestamp(txTimestamp);
            user.setAccountType("SAVING");
            user.setIdentity("USER");

            // 设置默认币种和初始余额
            user.setCurrency(new String[]{"CNY"});
            user.setBalance(new Double[]{initialBalance});

            // 初始化交易记录为空数组
            user.setTransactionRecords(new TransactionRecord[0]);

            // 如果设置了初始余额，创建一条充值记录
            if (initialBalance > 0) {
                TransactionRecord depositRecord = new TransactionRecord();
                depositRecord.setTransactionId(stub.getTxId());
                depositRecord.setTransactionType("INITIAL_DEPOSIT");
                depositRecord.setAmount(initialBalance);
                depositRecord.setDescription("账户创建时的初始充值");
                depositRecord.setStatus("COMPLETED");
                depositRecord.setTimestamp(txTimestamp);

                user.setTransactionRecords(new TransactionRecord[]{depositRecord});
            }

            // 调用createUser方法完成用户创建
            return createUser(ctx, user);
        } catch (Exception e) {
            String errorMessage = String.format("用户注册失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 生成随机16位卡号
     * 卡号格式：前6位固定前缀 + 8位时间戳 + 2位随机数
     *
     * @param userId 用户ID
     * @param timestamp 时间戳
     * @return 16位卡号
     */
    private String generateCardId(String userId, long timestamp) {
        // 卡号前缀，可以根据业务需求调整
        String prefix = "622848";

        // 使用时间戳的后8位
        String timestampStr = String.valueOf(timestamp);
        String timeComponent = timestampStr.length() > 8 ?
                              timestampStr.substring(timestampStr.length() - 8) :
                              String.format("%08d", timestamp);

        // 生成两位随机数
        int randomNum = (int) (Math.random() * 100);
        String randomComponent = String.format("%02d", randomNum);

        // 组合卡号
        return prefix + timeComponent + randomComponent;
    }

    /**
     * 用户注册功能
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param password 用户密码
     * @param accountType 账户类型（SAVING, CURRENT, FEX）
     * @param identity 身份类型（USER, BANK）
     * @return 注册结果
     */
    @Transaction(name = "RegisterUser", intent = Transaction.TYPE.SUBMIT)
    public String registerUser(final Context ctx, final String userId, final String password,
                              final String accountType, final String currency, final String identity) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 检查用户ID是否已存在
            String userKey = USER_PREFIX + userId;
            String existingUserJson = stub.getStringState(userKey);
            if (existingUserJson != null && !existingUserJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 已存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成随机16位卡号
            String cardId = generateCardId(userId, txTimestamp);

            // 创建用户实体
            UserEntity user = new UserEntity();
            user.setUserId(userId);
            user.setPassword(password);
            user.setCardId(cardId);
            user.setStatus(true);
            user.setCreatedTimestamp(txTimestamp);
            user.setCurrency(currency.split(","));

            // 设置账户类型
            if (accountType != null && !accountType.isEmpty()) {
                user.setAccountType(accountType);
            } else {
                // 默认设置为储蓄账户
                user.setAccountType("SAVING");
            }

            // 设置身份类型
            if (identity != null && !identity.isEmpty()) {
                user.setIdentity(identity);
            } else {
                // 默认设置为普通用户
                user.setIdentity("USER");
            }

            user.setBalance(new Double[]{0.0});

            // 初始化交易记录为空数组
            user.setTransactionRecords(new TransactionRecord[0]);

            // 调用createUser方法完成用户创建
            return createUser(ctx, user);
        } catch (Exception e) {
            String errorMessage = String.format("用户注册失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 用户登录功能
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param password 用户密码
     * @return 登录结果
     */
    @Transaction(name = "LoginUser", intent = Transaction.TYPE.EVALUATE)
    public String loginUser(final Context ctx, final String userId, final String password) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 构建用户键
            String userKey = USER_PREFIX + userId;

            // 从账本获取用户数据
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 解析用户数据
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 验证用户状态
            if (!user.isStatus()) {
                String errorMessage = String.format("用户ID %s 已被禁用", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证密码
            if (!user.getPassword().equals(password)) {
                String errorMessage = "密码错误";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 创建登录结果，过滤敏感信息
            Map<String, Object> loginResult = new HashMap<>();
            loginResult.put("userId", user.getUserId());
            loginResult.put("cardId", user.getCardId());
            loginResult.put("balance", user.getBalance());
            loginResult.put("currency", user.getCurrency());
            loginResult.put("accountType", user.getAccountType());
            loginResult.put("status", user.isStatus());
            loginResult.put("otherInfo", user.getOtherInfo());
            loginResult.put("loginTime", txTimestamp);

            log.info("用户登录成功: {}", userId);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "登录成功", loginResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("登录失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }


    /**
     * 用户存款功能
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param amount 存款金额
     * @param currencyCode 币种代码（可选，默认为第一种币种）
     * @param description 存款描述（可选）
     * @return 存款结果
     */
    @Transaction(name = "DepositFunds", intent = Transaction.TYPE.SUBMIT)
    public String depositFunds(final Context ctx, final String userId, final double amount,
                             final String currencyCode, final String description) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证金额是否大于0
            if (amount <= 0) {
                String errorMessage = "存款金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取用户信息
            String userKey = USER_PREFIX + userId;
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 验证用户状态
            if (!user.isStatus()) {
                String errorMessage = String.format("用户ID %s 已被禁用", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 确定币种索引
            int currencyIndex = 0; // 默认使用第一种币种
            if (currencyCode != null && !currencyCode.isEmpty()) {
                boolean currencyFound = false;
                for (int i = 0; i < user.getCurrency().length; i++) {
                    if (currencyCode.equals(user.getCurrency()[i])) {
                        currencyIndex = i;
                        currencyFound = true;
                        break;
                    }
                }

                // 如果指定的币种不存在，添加该币种
                if (!currencyFound) {
                    String[] newCurrencies = new String[user.getCurrency().length + 1];
                    Double[] newBalances = new Double[user.getBalance().length + 1];

                    // 复制原有币种和余额
                    for (int i = 0; i < user.getCurrency().length; i++) {
                        newCurrencies[i] = user.getCurrency()[i];
                        newBalances[i] = user.getBalance()[i];
                    }

                    // 添加新币种
                    newCurrencies[user.getCurrency().length] = currencyCode;
                    newBalances[user.getBalance().length] = 0.0; // 初始余额为0

                    user.setCurrency(newCurrencies);
                    user.setBalance(newBalances);

                    // 更新币种索引
                    currencyIndex = user.getCurrency().length - 1;
                }
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新用户余额
            Double[] userBalances = user.getBalance();
            // 处理可能的null值
            if (userBalances[currencyIndex] == null) {
                userBalances[currencyIndex] = 0.0;
            }
            userBalances[currencyIndex] += amount;
            user.setBalance(userBalances);

            // 创建存款交易记录
            TransactionRecord depositRecord = new TransactionRecord();
            depositRecord.setTransactionId(transactionId);
            depositRecord.setTransactionType("DEPOSIT");
            depositRecord.setCounterpartyId("null");
            depositRecord.setAmount(amount);

            // 设置描述
            String depositDescription = description != null && !description.isEmpty() ?
                                        description : "存款";
            depositDescription += " (币种: " + user.getCurrency()[currencyIndex] + ")";
            depositRecord.setDescription(depositDescription);

            depositRecord.setStatus("COMPLETED");
            depositRecord.setTimestamp(txTimestamp);

            // 添加交易记录到用户
            TransactionRecord[] userTransactions = user.getTransactionRecords();
            List<TransactionRecord> userTransactionsList = new ArrayList<>();
            if (userTransactions != null) {
                for (TransactionRecord record : userTransactions) {
                    userTransactionsList.add(record);
                }
            }
            userTransactionsList.add(depositRecord);
            user.setTransactionRecords(userTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));

            // 构建存款结果
            Map<String, Object> depositResult = new HashMap<>();
            depositResult.put("userId", userId);
            depositResult.put("amount", amount);
            depositResult.put("currency", user.getCurrency()[currencyIndex]);
            depositResult.put("newBalance", user.getBalance()[currencyIndex]);
            depositResult.put("transactionId", transactionId);
            depositResult.put("timestamp", txTimestamp);

            log.info("用户存款成功: {}, 金额: {}, 币种: {}", userId, amount, user.getCurrency()[currencyIndex]);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "存款成功", depositResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("存款失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 用户取款功能
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param amount 取款金额
     * @param currencyCode 币种代码（可选，默认为第一种币种）
     * @param description 取款描述（可选）
     * @return 取款结果
     */
    @Transaction(name = "WithdrawFunds", intent = Transaction.TYPE.SUBMIT)
    public String withdrawFunds(final Context ctx, final String userId, final double amount,
                              final String currencyCode, final String description) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证金额是否大于0
            if (amount <= 0) {
                String errorMessage = "取款金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取用户信息
            String userKey = USER_PREFIX + userId;
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 验证用户状态
            if (!user.isStatus()) {
                String errorMessage = String.format("用户ID %s 已被禁用", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 确定币种索引
            int currencyIndex = 0; // 默认使用第一种币种
            if (currencyCode != null && !currencyCode.isEmpty()) {
                boolean currencyFound = false;
                for (int i = 0; i < user.getCurrency().length; i++) {
                    if (currencyCode.equals(user.getCurrency()[i])) {
                        currencyIndex = i;
                        currencyFound = true;
                        break;
                    }
                }

                if (!currencyFound) {
                    String errorMessage = String.format("用户ID %s 没有币种 %s", userId, currencyCode);
                    log.error(errorMessage);
                    throw new ChaincodeException(errorMessage);
                }
            }

            // 验证用户余额是否足够
            Double userBalance = user.getBalance()[currencyIndex];
            // 处理可能的null值
            if (userBalance == null) {
                userBalance = 0.0;
                user.getBalance()[currencyIndex] = 0.0;
            }
            if (userBalance < amount) {
                String errorMessage = String.format("用户ID %s 的 %s 余额不足", userId, user.getCurrency()[currencyIndex]);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新用户余额
            Double[] userBalances = user.getBalance();
            // 处理可能的null值
            if (userBalances[currencyIndex] == null) {
                userBalances[currencyIndex] = 0.0;
            }
            userBalances[currencyIndex] -= amount;
            user.setBalance(userBalances);

            // 创建取款交易记录
            TransactionRecord withdrawRecord = new TransactionRecord();
            withdrawRecord.setTransactionId(transactionId);
            withdrawRecord.setTransactionType("WITHDRAW");
            withdrawRecord.setCounterpartyId("null");
            withdrawRecord.setAmount(amount);

            // 设置描述
            String withdrawDescription = description != null && !description.isEmpty() ?
                                         description : "取款";
            withdrawDescription += " (币种: " + user.getCurrency()[currencyIndex] + ")";
            withdrawRecord.setDescription(withdrawDescription);

            withdrawRecord.setStatus("COMPLETED");
            withdrawRecord.setTimestamp(txTimestamp);

            // 添加交易记录到用户
            TransactionRecord[] userTransactions = user.getTransactionRecords();
            List<TransactionRecord> userTransactionsList = new ArrayList<>();
            if (userTransactions != null) {
                for (TransactionRecord record : userTransactions) {
                    userTransactionsList.add(record);
                }
            }
            userTransactionsList.add(withdrawRecord);
            user.setTransactionRecords(userTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));

            // 构建取款结果
            Map<String, Object> withdrawResult = new HashMap<>();
            withdrawResult.put("userId", userId);
            withdrawResult.put("amount", amount);
            withdrawResult.put("currency", user.getCurrency()[currencyIndex]);
            withdrawResult.put("newBalance", user.getBalance()[currencyIndex]);
            withdrawResult.put("transactionId", transactionId);
            withdrawResult.put("timestamp", txTimestamp);

            log.info("用户取款成功: {}, 金额: {}, 币种: {}", userId, amount, user.getCurrency()[currencyIndex]);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "取款成功", withdrawResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("取款失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 用户转账功能，银行收取0.1%手续费（默认使用第一种币种）
     *
     * @param ctx 交易上下文
     * @param fromUserId 转出用户ID
     * @param toUserId 转入用户ID
     * @param bankUserId 银行用户ID（收取手续费）
     * @param amount 转账金额
     * @return 转账结果
     */
    @Transaction(name = "TransferFunds", intent = Transaction.TYPE.SUBMIT)
    public String transferFunds(final Context ctx, final String fromUserId, final String toUserId,
                               final String bankUserId, final double amount) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证转账金额是否大于0
            if (amount <= 0) {
                String errorMessage = "转账金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证转出和转入用户不能相同
            if (fromUserId.equals(toUserId)) {
                String errorMessage = "转出和转入用户不能相同";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取转出用户信息
            String fromUserKey = USER_PREFIX + fromUserId;
            String fromUserJson = stub.getStringState(fromUserKey);
            if (fromUserJson == null || fromUserJson.isEmpty()) {
                String errorMessage = String.format("转出用户ID %s 不存在", fromUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity fromUser = JSON.parseObject(fromUserJson, UserEntity.class);

            // 验证转出用户状态
            if (!fromUser.isStatus()) {
                String errorMessage = String.format("转出用户ID %s 已被禁用", fromUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取转入用户信息
            String toUserKey = USER_PREFIX + toUserId;
            String toUserJson = stub.getStringState(toUserKey);
            if (toUserJson == null || toUserJson.isEmpty()) {
                String errorMessage = String.format("转入用户ID %s 不存在", toUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity toUser = JSON.parseObject(toUserJson, UserEntity.class);

            // 验证转入用户状态
            if (!toUser.isStatus()) {
                String errorMessage = String.format("转入用户ID %s 已被禁用", toUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取银行用户信息
            String bankUserKey = USER_PREFIX + bankUserId;
            String bankUserJson = stub.getStringState(bankUserKey);
            if (bankUserJson == null || bankUserJson.isEmpty()) {
                String errorMessage = String.format("银行用户ID %s 不存在", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity bankUser = JSON.parseObject(bankUserJson, UserEntity.class);

            // 验证银行用户状态
            if (!bankUser.isStatus()) {
                String errorMessage = String.format("银行用户ID %s 已被禁用", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 计算手续费（0.1%）
            double feeRate = 0.001; // 0.1%
            double fee = amount * feeRate;
            double totalDeduction = amount + fee; // 转出用户需要扣除的总金额（转账金额+手续费）

            // 默认使用第一种币种进行转账
            if (fromUser.getCurrency() == null || fromUser.getCurrency().length == 0) {
                String errorMessage = String.format("转出用户ID %s 未设置币种", fromUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            String currencyCode = fromUser.getCurrency()[0];

            // 验证转出和转入用户的币种是否匹配
            boolean fromUserHasCurrency = false;
            int fromUserCurrencyIndex = -1;
            for (int i = 0; i < fromUser.getCurrency().length; i++) {
                if (fromUser.getCurrency()[i].equals(currencyCode)) {
                    fromUserHasCurrency = true;
                    fromUserCurrencyIndex = i;
                    break;
                }
            }

            if (!fromUserHasCurrency) {
                String errorMessage = String.format("转出用户ID %s 没有币种 %s", fromUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            boolean toUserHasCurrency = false;
            int toUserCurrencyIndex = -1;
            for (int i = 0; i < toUser.getCurrency().length; i++) {
                if (toUser.getCurrency()[i].equals(currencyCode)) {
                    toUserHasCurrency = true;
                    toUserCurrencyIndex = i;
                    break;
                }
            }

            if (!toUserHasCurrency) {
                String errorMessage = String.format("转入用户ID %s 没有币种 %s", toUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            boolean bankUserHasCurrency = false;
            int bankUserCurrencyIndex = -1;
            for (int i = 0; i < bankUser.getCurrency().length; i++) {
                if (bankUser.getCurrency()[i].equals(currencyCode)) {
                    bankUserHasCurrency = true;
                    bankUserCurrencyIndex = i;
                    break;
                }
            }

            if (!bankUserHasCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有币种 %s", bankUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证转出用户余额是否足够（包含手续费）
            Double fromUserBalance = fromUser.getBalance()[fromUserCurrencyIndex];
            // 处理可能的null值
            if (fromUserBalance == null) {
                fromUserBalance = 0.0;
                fromUser.getBalance()[fromUserCurrencyIndex] = 0.0;
            }
            if (fromUserBalance < totalDeduction) {
                String errorMessage = String.format("转出用户ID %s 的 %s 余额不足（需要支付转账金额和手续费）", fromUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新转出用户余额（扣除转账金额和手续费）
            Double[] fromUserBalances = fromUser.getBalance();
            // 处理可能的null值
            if (fromUserBalances[fromUserCurrencyIndex] == null) {
                fromUserBalances[fromUserCurrencyIndex] = 0.0;
            }
            fromUserBalances[fromUserCurrencyIndex] = fromUserBalance - totalDeduction;
            fromUser.setBalance(fromUserBalances);

            // 创建转出用户的交易记录
            TransactionRecord fromUserRecord = new TransactionRecord();
            fromUserRecord.setTransactionId(transactionId);
            fromUserRecord.setTransactionType("TRANSFER");
            fromUserRecord.setAmount(amount);
            fromUserRecord.setDescription(" (已扣除0.1%手续费: " + fee + ")");
            fromUserRecord.setCounterpartyId(toUserId);
            fromUserRecord.setStatus("COMPLETED");
            fromUserRecord.setTimestamp(txTimestamp);

            // 添加交易记录到转出用户
            TransactionRecord[] fromUserTransactions = fromUser.getTransactionRecords();
            List<TransactionRecord> fromUserTransactionsList = new ArrayList<>();
            if (fromUserTransactions != null) {
                for (TransactionRecord record : fromUserTransactions) {
                    fromUserTransactionsList.add(record);
                }
            }
            fromUserTransactionsList.add(fromUserRecord);
            fromUser.setTransactionRecords(fromUserTransactionsList.toArray(new TransactionRecord[0]));

            // 更新转入用户余额（收到全部转账金额，手续费由转出用户支付）
            Double[] toUserBalances = toUser.getBalance();
            // 处理可能的null值
            if (toUserBalances[toUserCurrencyIndex] == null) {
                toUserBalances[toUserCurrencyIndex] = 0.0;
            }
            toUserBalances[toUserCurrencyIndex] += amount;
            toUser.setBalance(toUserBalances);

            // 创建转入用户的交易记录
            TransactionRecord toUserRecord = new TransactionRecord();
            toUserRecord.setTransactionId(transactionId);
            toUserRecord.setTransactionType("RECEIVE");
            toUserRecord.setAmount(amount);
            toUserRecord.setDescription(" (收到全部转账金额，手续费由转出方支付)");
            toUserRecord.setCounterpartyId(fromUserId);
            toUserRecord.setStatus("COMPLETED");
            toUserRecord.setTimestamp(txTimestamp);

            // 添加交易记录到转入用户
            TransactionRecord[] toUserTransactions = toUser.getTransactionRecords();
            List<TransactionRecord> toUserTransactionsList = new ArrayList<>();
            if (toUserTransactions != null) {
                for (TransactionRecord record : toUserTransactions) {
                    toUserTransactionsList.add(record);
                }
            }
            toUserTransactionsList.add(toUserRecord);
            toUser.setTransactionRecords(toUserTransactionsList.toArray(new TransactionRecord[0]));

            // 更新银行用户余额（添加手续费）
            Double[] bankUserBalances = bankUser.getBalance();
            // 处理可能的null值
            if (bankUserBalances[bankUserCurrencyIndex] == null) {
                bankUserBalances[bankUserCurrencyIndex] = 0.0;
            }
            bankUserBalances[bankUserCurrencyIndex] += fee;
            bankUser.setBalance(bankUserBalances);

            // 创建银行用户的交易记录
            TransactionRecord bankUserRecord = new TransactionRecord();
            bankUserRecord.setTransactionId(transactionId);
            bankUserRecord.setTransactionType("FEE");
            bankUserRecord.setAmount(fee);
            bankUserRecord.setDescription("转账手续费 (从 " + fromUserId + " 到 " + toUserId + " 的转账)");
            bankUserRecord.setCounterpartyId(fromUserId);
            bankUserRecord.setStatus("COMPLETED");
            bankUserRecord.setTimestamp(txTimestamp);

            // 添加交易记录到银行用户
            TransactionRecord[] bankUserTransactions = bankUser.getTransactionRecords();
            List<TransactionRecord> bankUserTransactionsList = new ArrayList<>();
            if (bankUserTransactions != null) {
                for (TransactionRecord record : bankUserTransactions) {
                    bankUserTransactionsList.add(record);
                }
            }
            bankUserTransactionsList.add(bankUserRecord);
            bankUser.setTransactionRecords(bankUserTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户对象序列化为JSON并存储到账本
            stub.putStringState(fromUserKey, JSON.toJSONString(fromUser));
            stub.putStringState(toUserKey, JSON.toJSONString(toUser));
            stub.putStringState(bankUserKey, JSON.toJSONString(bankUser));

            // 构建转账结果
            Map<String, Object> transferResult = new HashMap<>();
            transferResult.put("transactionId", transactionId);
            transferResult.put("fromUserId", fromUserId);
            transferResult.put("toUserId", toUserId);
            transferResult.put("bankUserId", bankUserId);
            transferResult.put("totalAmount", amount);
            transferResult.put("totalDeduction", totalDeduction);
            transferResult.put("fee", fee);
            transferResult.put("feeRate", "0.1%");
            transferResult.put("currency", currencyCode);
            transferResult.put("timestamp", txTimestamp);
            transferResult.put("status", "COMPLETED");

            // 发布原有转账事件
            stub.setEvent("transfer", JSON.toJSONString(transferResult).getBytes(StandardCharsets.UTF_8));

            // 构建详细的转账事件数据
            Map<String, Object> detailedTransferEvent = new HashMap<>(transferResult);
            // 添加转出用户余额变化信息
            detailedTransferEvent.put("fromUserBalanceBefore", fromUserBalance);
            detailedTransferEvent.put("fromUserBalanceAfter", fromUserBalance - totalDeduction);
            detailedTransferEvent.put("fromUserBalanceChange", -totalDeduction);

            // 添加转入用户余额变化信息
            Double toUserBalanceBefore = toUser.getBalance()[toUserCurrencyIndex];
            if (toUserBalanceBefore == null) {
                toUserBalanceBefore = 0.0;
            }
            detailedTransferEvent.put("toUserBalanceBefore", toUserBalanceBefore);
            detailedTransferEvent.put("toUserBalanceAfter", toUserBalanceBefore + amount);
            detailedTransferEvent.put("toUserBalanceChange", amount);

            // 添加银行手续费信息
            Double bankBalanceBefore = bankUser.getBalance()[bankUserCurrencyIndex];
            if (bankBalanceBefore == null) {
                bankBalanceBefore = 0.0;
            }
            detailedTransferEvent.put("bankBalanceBefore", bankBalanceBefore);
            detailedTransferEvent.put("bankBalanceAfter", bankBalanceBefore + fee);
            detailedTransferEvent.put("bankBalanceChange", fee);

            // 添加默认交易描述
            detailedTransferEvent.put("description", "转账");

            // 发布详细转账事件
            stub.setEvent("transfer_detailed_event", JSON.toJSONString(detailedTransferEvent).getBytes(StandardCharsets.UTF_8));

            log.info("转账成功: 从 {} 到 {}, 币种: {}, 转账金额: {}, 总扣除金额: {}, 手续费: {}",
                    fromUserId, toUserId, currencyCode, amount, totalDeduction, fee);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "转账成功", transferResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("转账失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 用户购买外汇功能
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param bankUserId 银行用户ID
     * @param baseCurrencyCode 基准币种代码（用户使用的本币）
     * @param targetCurrencyCode 目标币种代码（要购买的外币）
     * @param targetAmount 目标币种金额（要购买的外币金额）
     * @param rateId 汇率表ID
     * @return 购买结果
     */
    @Transaction(name = "BuyForeignCurrency", intent = Transaction.TYPE.SUBMIT)
    public String buyForeignCurrency(final Context ctx, final String userId, final String bankUserId,
                                   final String baseCurrencyCode, final String targetCurrencyCode,
                                   final double targetAmount, final String rateId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证金额是否大于0
            if (targetAmount <= 0) {
                String errorMessage = "购买金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取用户信息
            String userKey = USER_PREFIX + userId;
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 验证用户状态
            if (!user.isStatus()) {
                String errorMessage = String.format("用户ID %s 已被禁用", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取银行用户信息
            String bankUserKey = USER_PREFIX + bankUserId;
            String bankUserJson = stub.getStringState(bankUserKey);
            if (bankUserJson == null || bankUserJson.isEmpty()) {
                String errorMessage = String.format("银行用户ID %s 不存在", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity bankUser = JSON.parseObject(bankUserJson, UserEntity.class);

            // 验证银行用户状态
            if (!bankUser.isStatus()) {
                String errorMessage = String.format("银行用户ID %s 已被禁用", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取汇率表信息
            String rateKey = EXCHANGE_RATE_PREFIX + rateId;
            String rateJson = stub.getStringState(rateKey);
            if (rateJson == null || rateJson.isEmpty()) {
                String errorMessage = String.format("汇率表ID %s 不存在", rateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity exchangeRate = JSON.parseObject(rateJson, ExchangeRateEntity.class);

            // 验证汇率表状态
            if (!exchangeRate.isStatus()) {
                String errorMessage = String.format("汇率表ID %s 已失效", rateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证汇率表的币种与目标币种是否匹配
            if (!exchangeRate.getCurrencyCode().equals(targetCurrencyCode)) {
                String errorMessage = String.format("汇率表ID %s 的币种 %s 与目标币种 %s 不匹配",
                        rateId, exchangeRate.getCurrencyCode(), targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证用户是否支持基准币种和目标币种
            boolean userHasBaseCurrency = false;
            int userBaseCurrencyIndex = -1;
            boolean userHasTargetCurrency = false;
            int userTargetCurrencyIndex = -1;

            for (int i = 0; i < user.getCurrency().length; i++) {
                if (user.getCurrency()[i].equals(baseCurrencyCode)) {
                    userHasBaseCurrency = true;
                    userBaseCurrencyIndex = i;
                }
                if (user.getCurrency()[i].equals(targetCurrencyCode)) {
                    userHasTargetCurrency = true;
                    userTargetCurrencyIndex = i;
                }
            }

            if (!userHasBaseCurrency) {
                String errorMessage = String.format("用户ID %s 没有基准币种 %s", userId, baseCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 如果用户没有目标币种，需要添加该币种
            if (!userHasTargetCurrency) {
                // 添加新币种
                String[] newCurrencies = new String[user.getCurrency().length + 1];
                Double[] newBalances = new Double[user.getBalance().length + 1];

                // 复制原有币种和余额
                for (int i = 0; i < user.getCurrency().length; i++) {
                    newCurrencies[i] = user.getCurrency()[i];
                    newBalances[i] = user.getBalance()[i];
                }

                // 添加新币种
                newCurrencies[user.getCurrency().length] = targetCurrencyCode;
                newBalances[user.getBalance().length] = 0.0; // 初始余额为0

                user.setCurrency(newCurrencies);
                user.setBalance(newBalances);

                // 更新目标币种索引
                userTargetCurrencyIndex = user.getCurrency().length - 1;
            }

            // 验证银行是否支持基准币种和目标币种
            boolean bankHasBaseCurrency = false;
            int bankBaseCurrencyIndex = -1;
            boolean bankHasTargetCurrency = false;
            int bankTargetCurrencyIndex = -1;

            for (int i = 0; i < bankUser.getCurrency().length; i++) {
                if (bankUser.getCurrency()[i].equals(baseCurrencyCode)) {
                    bankHasBaseCurrency = true;
                    bankBaseCurrencyIndex = i;
                }
                if (bankUser.getCurrency()[i].equals(targetCurrencyCode)) {
                    bankHasTargetCurrency = true;
                    bankTargetCurrencyIndex = i;
                }
            }

            if (!bankHasBaseCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有基准币种 %s", bankUserId, baseCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            if (!bankHasTargetCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有目标币种 %s", bankUserId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 计算需要支付的基准币种金额（使用银行卖出价）
            double baseAmount = targetAmount * exchangeRate.getBankSellRate();

            // 验证用户基准币种余额是否足够
            Double userBaseBalance = user.getBalance()[userBaseCurrencyIndex];
            // 处理可能的null值
            if (userBaseBalance == null) {
                userBaseBalance = 0.0;
                user.getBalance()[userBaseCurrencyIndex] = 0.0;
            }
            if (userBaseBalance < baseAmount) {
                String errorMessage = String.format("用户ID %s 的 %s 余额不足（需要 %s %s）", userId, baseCurrencyCode, baseAmount, baseCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证银行目标币种余额是否足够
            Double bankTargetBalance = bankUser.getBalance()[bankTargetCurrencyIndex];
            // 处理可能的null值
            if (bankTargetBalance == null) {
                bankTargetBalance = 0.0;
                bankUser.getBalance()[bankTargetCurrencyIndex] = 0.0;
            }
            if (bankTargetBalance < targetAmount) {
                String errorMessage = String.format("银行用户ID %s 的 %s 余额不足", bankUserId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新用户基准币种余额（减少）
            Double[] userBalances = user.getBalance();
            // 处理可能的null值
            if (userBalances[userBaseCurrencyIndex] == null) {
                userBalances[userBaseCurrencyIndex] = 0.0;
            }
            userBalances[userBaseCurrencyIndex] -= baseAmount;

            // 更新用户目标币种余额（增加）
            if (userBalances[userTargetCurrencyIndex] == null) {
                userBalances[userTargetCurrencyIndex] = 0.0;
            }
            userBalances[userTargetCurrencyIndex] += targetAmount;

            // 更新银行基准币种余额（增加）
            Double[] bankBalances = bankUser.getBalance();
            // 处理可能的null值
            if (bankBalances[bankBaseCurrencyIndex] == null) {
                bankBalances[bankBaseCurrencyIndex] = 0.0;
            }
            bankBalances[bankBaseCurrencyIndex] += baseAmount;

            // 更新银行目标币种余额（减少）
            if (bankBalances[bankTargetCurrencyIndex] == null) {
                bankBalances[bankTargetCurrencyIndex] = 0.0;
            }
            bankBalances[bankTargetCurrencyIndex] -= targetAmount;

            // 创建用户的交易记录
            TransactionRecord userRecord = new TransactionRecord();
            userRecord.setTransactionId(transactionId);
            userRecord.setTransactionType("BUY_FOREIGN_CURRENCY");
            userRecord.setAmount(baseAmount);
            userRecord.setDescription("购买外汇: 支付 " + baseAmount + " " + baseCurrencyCode + ", 获得 " + targetAmount + " " + targetCurrencyCode + ", 汇率: " + exchangeRate.getBankSellRate() + " " + baseCurrencyCode + "/" + targetCurrencyCode);
            userRecord.setCounterpartyId(bankUserId);
            userRecord.setStatus("COMPLETED");
            userRecord.setTimestamp(txTimestamp);

            // 添加交易记录到用户
            TransactionRecord[] userTransactions = user.getTransactionRecords();
            List<TransactionRecord> userTransactionsList = new ArrayList<>();
            if (userTransactions != null) {
                for (TransactionRecord record : userTransactions) {
                    userTransactionsList.add(record);
                }
            }
            userTransactionsList.add(userRecord);
            user.setTransactionRecords(userTransactionsList.toArray(new TransactionRecord[0]));

            // 创建银行的交易记录
            TransactionRecord bankRecord = new TransactionRecord();
            bankRecord.setTransactionId(transactionId);
            bankRecord.setTransactionType("SELL_FOREIGN_CURRENCY");
            bankRecord.setAmount(targetAmount);
            bankRecord.setDescription("卖出外汇: 提供 " + targetAmount + " " + targetCurrencyCode + ", 收到 " + baseAmount + " " + baseCurrencyCode + ", 汇率: " + exchangeRate.getBankSellRate() + " " + baseCurrencyCode + "/" + targetCurrencyCode);
            bankRecord.setCounterpartyId(userId);
            bankRecord.setStatus("COMPLETED");
            bankRecord.setTimestamp(txTimestamp);

            // 添加交易记录到银行
            TransactionRecord[] bankTransactions = bankUser.getTransactionRecords();
            List<TransactionRecord> bankTransactionsList = new ArrayList<>();
            if (bankTransactions != null) {
                for (TransactionRecord record : bankTransactions) {
                    bankTransactionsList.add(record);
                }
            }
            bankTransactionsList.add(bankRecord);
            bankUser.setTransactionRecords(bankTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户和银行对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));
            stub.putStringState(bankUserKey, JSON.toJSONString(bankUser));

            // 构建购买结果
            Map<String, Object> buyResult = new HashMap<>();
            buyResult.put("transactionId", transactionId);
            buyResult.put("userId", userId);
            buyResult.put("bankUserId", bankUserId);
            buyResult.put("baseCurrency", baseCurrencyCode);
            buyResult.put("targetCurrency", targetCurrencyCode);
            buyResult.put("baseAmount", baseAmount);
            buyResult.put("targetAmount", targetAmount);
            buyResult.put("exchangeRate", exchangeRate.getBankSellRate());
            buyResult.put("timestamp", txTimestamp);
            buyResult.put("status", "COMPLETED");

            stub.setEvent("buy_foreign_currency", JSON.toJSONString(buyResult).getBytes(StandardCharsets.UTF_8));

            log.info("购买外汇成功: 用户 {} 支付 {} {}, 获得 {} {}, 汇率: {} {}/{}",
                    userId, baseAmount, baseCurrencyCode, targetAmount, targetCurrencyCode, exchangeRate.getBankSellRate(), baseCurrencyCode, targetCurrencyCode);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "购买外汇成功", buyResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("购买外汇失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 用户转账功能，银行收取0.1%手续费（指定币种）
     *
     * @param ctx 交易上下文
     * @param fromUserId 转出用户ID
     * @param toUserId 转入用户ID
     * @param bankUserId 银行用户ID（收取手续费）
     * @param amount 转账金额
     * @param currencyCode 币种代码
     * @param description 转账描述
     * @return 转账结果
     */
    @Transaction(name = "TransferFundsWithCurrency", intent = Transaction.TYPE.SUBMIT)
    public String transferFundsWithCurrency(final Context ctx, final String fromUserId, final String toUserId,
                                          final String bankUserId, final double amount, final String currencyCode,
                                          final String description) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证转账金额是否大于0
            if (amount <= 0) {
                String errorMessage = "转账金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证转出和转入用户不能相同
            if (fromUserId.equals(toUserId)) {
                String errorMessage = "转出和转入用户不能相同";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证币种代码不能为空
            if (currencyCode == null || currencyCode.isEmpty()) {
                String errorMessage = "币种代码不能为空";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取转出用户信息
            String fromUserKey = USER_PREFIX + fromUserId;
            String fromUserJson = stub.getStringState(fromUserKey);
            if (fromUserJson == null || fromUserJson.isEmpty()) {
                String errorMessage = String.format("转出用户ID %s 不存在", fromUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity fromUser = JSON.parseObject(fromUserJson, UserEntity.class);

            // 验证转出用户状态
            if (!fromUser.isStatus()) {
                String errorMessage = String.format("转出用户ID %s 已被禁用", fromUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取转入用户信息
            String toUserKey = USER_PREFIX + toUserId;
            String toUserJson = stub.getStringState(toUserKey);
            if (toUserJson == null || toUserJson.isEmpty()) {
                String errorMessage = String.format("转入用户ID %s 不存在", toUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity toUser = JSON.parseObject(toUserJson, UserEntity.class);

            // 验证转入用户状态
            if (!toUser.isStatus()) {
                String errorMessage = String.format("转入用户ID %s 已被禁用", toUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取银行用户信息
            String bankUserKey = USER_PREFIX + bankUserId;
            String bankUserJson = stub.getStringState(bankUserKey);
            if (bankUserJson == null || bankUserJson.isEmpty()) {
                String errorMessage = String.format("银行用户ID %s 不存在", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity bankUser = JSON.parseObject(bankUserJson, UserEntity.class);

            // 验证银行用户状态
            if (!bankUser.isStatus()) {
                String errorMessage = String.format("银行用户ID %s 已被禁用", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 计算手续费（0.1%）
            double feeRate = 0.001; // 0.1%
            double fee = amount * feeRate;
            double totalDeduction = amount + fee; // 转出用户需要扣除的总金额（转账金额+手续费）

            // 验证转出和转入用户的币种是否匹配
            boolean fromUserHasCurrency = false;
            int fromUserCurrencyIndex = -1;
            for (int i = 0; i < fromUser.getCurrency().length; i++) {
                if (fromUser.getCurrency()[i].equals(currencyCode)) {
                    fromUserHasCurrency = true;
                    fromUserCurrencyIndex = i;
                    break;
                }
            }

            if (!fromUserHasCurrency) {
                String errorMessage = String.format("转出用户ID %s 没有币种 %s", fromUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            boolean toUserHasCurrency = false;
            int toUserCurrencyIndex = -1;
            for (int i = 0; i < toUser.getCurrency().length; i++) {
                if (toUser.getCurrency()[i].equals(currencyCode)) {
                    toUserHasCurrency = true;
                    toUserCurrencyIndex = i;
                    break;
                }
            }

            if (!toUserHasCurrency) {
                String errorMessage = String.format("转入用户ID %s 没有币种 %s", toUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            boolean bankUserHasCurrency = false;
            int bankUserCurrencyIndex = -1;
            for (int i = 0; i < bankUser.getCurrency().length; i++) {
                if (bankUser.getCurrency()[i].equals(currencyCode)) {
                    bankUserHasCurrency = true;
                    bankUserCurrencyIndex = i;
                    break;
                }
            }

            if (!bankUserHasCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有币种 %s", bankUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证转出用户余额是否足够（包含手续费）
            double fromUserBalance = fromUser.getBalance()[fromUserCurrencyIndex];
            if (fromUserBalance < totalDeduction) {
                String errorMessage = String.format("转出用户ID %s 的 %s 余额不足（需要支付转账金额和手续费）", fromUserId, currencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新转出用户余额（扣除转账金额和手续费）
            Double[] fromUserBalances = fromUser.getBalance();
            fromUserBalances[fromUserCurrencyIndex] = fromUserBalance - totalDeduction;
            fromUser.setBalance(fromUserBalances);

            // 创建转出用户的交易记录
            TransactionRecord fromUserRecord = new TransactionRecord();
            fromUserRecord.setTransactionId(transactionId);
            fromUserRecord.setTransactionType("TRANSFER");
            fromUserRecord.setAmount(amount);
            fromUserRecord.setDescription(description + " (已扣除0.1%手续费: " + fee + ", 币种: " + currencyCode + ")");
            fromUserRecord.setCounterpartyId(toUserId);
            fromUserRecord.setStatus("COMPLETED");
            fromUserRecord.setTimestamp(txTimestamp);

            // 添加交易记录到转出用户
            TransactionRecord[] fromUserTransactions = fromUser.getTransactionRecords();
            List<TransactionRecord> fromUserTransactionsList = new ArrayList<>();
            if (fromUserTransactions != null) {
                for (TransactionRecord record : fromUserTransactions) {
                    fromUserTransactionsList.add(record);
                }
            }
            fromUserTransactionsList.add(fromUserRecord);
            fromUser.setTransactionRecords(fromUserTransactionsList.toArray(new TransactionRecord[0]));

            // 更新转入用户余额（收到全部转账金额，手续费由转出用户支付）
            Double[] toUserBalances = toUser.getBalance();
            toUserBalances[toUserCurrencyIndex] += amount;
            toUser.setBalance(toUserBalances);

            // 创建转入用户的交易记录
            TransactionRecord toUserRecord = new TransactionRecord();
            toUserRecord.setTransactionId(transactionId);
            toUserRecord.setTransactionType("RECEIVE");
            toUserRecord.setAmount(amount);
            toUserRecord.setDescription(description + " (收到全部转账金额，手续费由转出方支付, 币种: " + currencyCode + ")");
            toUserRecord.setCounterpartyId(fromUserId);
            toUserRecord.setStatus("COMPLETED");
            toUserRecord.setTimestamp(txTimestamp);

            // 添加交易记录到转入用户
            TransactionRecord[] toUserTransactions = toUser.getTransactionRecords();
            List<TransactionRecord> toUserTransactionsList = new ArrayList<>();
            if (toUserTransactions != null) {
                for (TransactionRecord record : toUserTransactions) {
                    toUserTransactionsList.add(record);
                }
            }
            toUserTransactionsList.add(toUserRecord);
            toUser.setTransactionRecords(toUserTransactionsList.toArray(new TransactionRecord[0]));

            // 更新银行用户余额（添加手续费）
            Double[] bankUserBalances = bankUser.getBalance();
            bankUserBalances[bankUserCurrencyIndex] += fee;
            bankUser.setBalance(bankUserBalances);

            // 创建银行用户的交易记录
            TransactionRecord bankUserRecord = new TransactionRecord();
            bankUserRecord.setTransactionId(transactionId);
            bankUserRecord.setTransactionType("FEE");
            bankUserRecord.setAmount(fee);
            bankUserRecord.setDescription("转账手续费 (从 " + fromUserId + " 到 " + toUserId + " 的转账, 币种: " + currencyCode + ")");
            bankUserRecord.setCounterpartyId(fromUserId);
            bankUserRecord.setStatus("COMPLETED");
            bankUserRecord.setTimestamp(txTimestamp);

            // 添加交易记录到银行用户
            TransactionRecord[] bankUserTransactions = bankUser.getTransactionRecords();
            List<TransactionRecord> bankUserTransactionsList = new ArrayList<>();
            if (bankUserTransactions != null) {
                for (TransactionRecord record : bankUserTransactions) {
                    bankUserTransactionsList.add(record);
                }
            }
            bankUserTransactionsList.add(bankUserRecord);
            bankUser.setTransactionRecords(bankUserTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户对象序列化为JSON并存储到账本
            stub.putStringState(fromUserKey, JSON.toJSONString(fromUser));
            stub.putStringState(toUserKey, JSON.toJSONString(toUser));
            stub.putStringState(bankUserKey, JSON.toJSONString(bankUser));

            // 构建转账结果
            Map<String, Object> transferResult = new HashMap<>();
            transferResult.put("transactionId", transactionId);
            transferResult.put("fromUserId", fromUserId);
            transferResult.put("toUserId", toUserId);
            transferResult.put("bankUserId", bankUserId);
            transferResult.put("totalAmount", amount);
            transferResult.put("totalDeduction", totalDeduction);
            transferResult.put("fee", fee);
            transferResult.put("feeRate", "0.1%");
            transferResult.put("currency", currencyCode);
            transferResult.put("timestamp", txTimestamp);
            transferResult.put("status", "COMPLETED");

            // 发布原有转账事件
            stub.setEvent("transfer_with_currency", JSON.toJSONString(transferResult).getBytes(StandardCharsets.UTF_8));

            // 构建详细的转账事件数据
            Map<String, Object> detailedTransferEvent = new HashMap<>(transferResult);
            // 添加转出用户余额变化信息
            detailedTransferEvent.put("fromUserBalanceBefore", fromUserBalance);
            detailedTransferEvent.put("fromUserBalanceAfter", fromUserBalance - totalDeduction);
            detailedTransferEvent.put("fromUserBalanceChange", -totalDeduction);

            // 添加转入用户余额变化信息
            Double toUserBalanceBefore = toUser.getBalance()[toUserCurrencyIndex];
            if (toUserBalanceBefore == null) {
                toUserBalanceBefore = 0.0;
            }
            detailedTransferEvent.put("toUserBalanceBefore", toUserBalanceBefore);
            detailedTransferEvent.put("toUserBalanceAfter", toUserBalanceBefore + amount);
            detailedTransferEvent.put("toUserBalanceChange", amount);

            // 添加银行手续费信息
            Double bankBalanceBefore = bankUser.getBalance()[bankUserCurrencyIndex];
            if (bankBalanceBefore == null) {
                bankBalanceBefore = 0.0;
            }
            detailedTransferEvent.put("bankBalanceBefore", bankBalanceBefore);
            detailedTransferEvent.put("bankBalanceAfter", bankBalanceBefore + fee);
            detailedTransferEvent.put("bankBalanceChange", fee);

            // 添加交易描述
            detailedTransferEvent.put("description", description);

            // 发布详细转账事件
            stub.setEvent("transfer_detailed_event", JSON.toJSONString(detailedTransferEvent).getBytes(StandardCharsets.UTF_8));

            log.info("转账成功: 从 {} 到 {}, 币种: {}, 转账金额: {}, 总扣除金额: {}, 手续费: {}",
                    fromUserId, toUserId, currencyCode, amount, totalDeduction, fee);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "转账成功", transferResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("转账失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 根据条件分页查询汇率表
     *
     * @param ctx 交易上下文
     * @param queryString 查询条件JSON字符串
     * @param pageSize 每页记录数
     * @param bookmark 分页书签
     * @return 查询结果
     */
    @Transaction(name = "QueryExchangeRates", intent = Transaction.TYPE.EVALUATE)
    public String queryExchangeRates(final Context ctx, final String queryString, final int pageSize, final String bookmark) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 解析查询条件
            JSONObject queryJson = JSON.parseObject(queryString);

            // 构建CouchDB查询语句
            StringBuilder queryBuilder = new StringBuilder("{\"selector\":{");

            // 添加前缀条件，确保只查询汇率表对象
            queryBuilder.append("\"_id\":{\"$regex\":\"^").append(EXCHANGE_RATE_PREFIX).append("\"}");

            // 添加其他查询条件
            if (queryJson != null && !queryJson.isEmpty()) {
                for (Map.Entry<String, Object> entry : queryJson.entrySet()) {
                    queryBuilder.append(",\"").append(entry.getKey()).append("\":");

                    // 根据值类型构建不同的查询条件
                    if (entry.getValue() instanceof String) {
                        queryBuilder.append("\"").append(entry.getValue()).append("\"");
                    } else {
                        queryBuilder.append(entry.getValue());
                    }
                }
            }

            queryBuilder.append("}}");

            // 执行分页查询
            QueryResultsIteratorWithMetadata<KeyValue> queryResults = stub.getQueryResultWithPagination(queryBuilder.toString(), pageSize, bookmark);

            // 处理查询结果
            List<ExchangeRateEntity> exchangeRates = new ArrayList<>();
            for (KeyValue kv : queryResults) {
                ExchangeRateEntity rate = JSON.parseObject(kv.getStringValue(), ExchangeRateEntity.class);
                exchangeRates.add(rate);
            }

            // 构建分页结果
            Map<String, Object> paginationResult = new HashMap<>();
            paginationResult.put("records", exchangeRates);
            paginationResult.put("recordCount", exchangeRates.size());
            paginationResult.put("bookmark", queryResults.getMetadata().getBookmark());
            paginationResult.put("fetchedRecordsCount", queryResults.getMetadata().getFetchedRecordsCount());

            log.info("查询汇率表成功，返回记录数: {}", exchangeRates.size());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "查询汇率表成功", paginationResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("查询汇率表失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 计算跨币种汇率转换
     *
     * @param ctx 交易上下文
     * @param sourceCurrencyCode 源币种代码
     * @param targetCurrencyCode 目标币种代码
     * @param intermediateCurrencyCode 中间币种代码（通常是基准币种，如CNY）
     * @param sourceAmount 源币种金额
     * @param sourceRateId 源币种汇率表ID
     * @param targetRateId 目标币种汇率表ID
     * @param operationType 操作类型（BUY或SELL）
     * @return 计算结果
     */
    @Transaction(name = "CalculateCrossRateConversion", intent = Transaction.TYPE.EVALUATE)
    public String calculateCrossRateConversion(final Context ctx, final String sourceCurrencyCode, final String targetCurrencyCode,
                                            final String intermediateCurrencyCode, final double sourceAmount,
                                            final String sourceRateId, final String targetRateId, final String operationType) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证金额是否大于0
            if (sourceAmount <= 0) {
                String errorMessage = "金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证操作类型
            if (!"BUY".equalsIgnoreCase(operationType) && !"SELL".equalsIgnoreCase(operationType)) {
                String errorMessage = "操作类型必须是BUY或SELL";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取源币种汇率表
            String sourceRateKey = EXCHANGE_RATE_PREFIX + sourceRateId;
            String sourceRateJson = stub.getStringState(sourceRateKey);
            if (sourceRateJson == null || sourceRateJson.isEmpty()) {
                String errorMessage = String.format("源币种汇率表ID %s 不存在", sourceRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity sourceRate = JSON.parseObject(sourceRateJson, ExchangeRateEntity.class);

            // 验证源币种汇率表状态
            if (!sourceRate.isStatus()) {
                String errorMessage = String.format("源币种汇率表ID %s 已失效", sourceRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证源币种汇率表的币种与源币种是否匹配
            if (!sourceRate.getCurrencyCode().equals(sourceCurrencyCode)) {
                String errorMessage = String.format("源币种汇率表ID %s 的币种 %s 与源币种 %s 不匹配",
                        sourceRateId, sourceRate.getCurrencyCode(), sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取目标币种汇率表
            String targetRateKey = EXCHANGE_RATE_PREFIX + targetRateId;
            String targetRateJson = stub.getStringState(targetRateKey);
            if (targetRateJson == null || targetRateJson.isEmpty()) {
                String errorMessage = String.format("目标币种汇率表ID %s 不存在", targetRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity targetRate = JSON.parseObject(targetRateJson, ExchangeRateEntity.class);

            // 验证目标币种汇率表状态
            if (!targetRate.isStatus()) {
                String errorMessage = String.format("目标币种汇率表ID %s 已失效", targetRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证目标币种汇率表的币种与目标币种是否匹配
            if (!targetRate.getCurrencyCode().equals(targetCurrencyCode)) {
                String errorMessage = String.format("目标币种汇率表ID %s 的币种 %s 与目标币种 %s 不匹配",
                        targetRateId, targetRate.getCurrencyCode(), targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            double intermediateAmount;
            double targetAmount;

            // 根据操作类型选择不同的汇率计算方式
            if ("BUY".equalsIgnoreCase(operationType)) {
                // 购买外汇：使用银行卖出价
                // 源币种→中间币种（购汇）：使用银行卖出价
                intermediateAmount = sourceAmount * sourceRate.getBankSellRate();

                // 中间币种→目标币种（结汇）：使用银行买入价的倒数
                targetAmount = intermediateAmount / targetRate.getBankBuyRate();
            } else {
                // 卖出外汇：使用银行买入价
                // 源币种→中间币种（结汇）：使用银行买入价
                intermediateAmount = sourceAmount * sourceRate.getBankBuyRate();

                // 中间币种→目标币种（购汇）：使用银行卖出价的倒数
                targetAmount = intermediateAmount / targetRate.getBankSellRate();
            }

            // 构建计算结果
            Map<String, Object> calculationResult = new HashMap<>();
            calculationResult.put("sourceCurrency", sourceCurrencyCode);
            calculationResult.put("targetCurrency", targetCurrencyCode);
            calculationResult.put("intermediateCurrency", intermediateCurrencyCode);
            calculationResult.put("sourceAmount", sourceAmount);
            calculationResult.put("intermediateAmount", intermediateAmount);
            calculationResult.put("targetAmount", targetAmount);
            calculationResult.put("operationType", operationType);
            calculationResult.put("sourceRate", operationType.equalsIgnoreCase("BUY") ? sourceRate.getBankSellRate() : sourceRate.getBankBuyRate());
            calculationResult.put("targetRate", operationType.equalsIgnoreCase("BUY") ? targetRate.getBankBuyRate() : targetRate.getBankSellRate());
            calculationResult.put("timestamp", txTimestamp);

            String description;
            if ("BUY".equalsIgnoreCase(operationType)) {
                description = String.format("购买 %.2f %s 需要 %.2f %s，通过中间币种 %s（%.2f）转换",
                        sourceAmount, sourceCurrencyCode, targetAmount, targetCurrencyCode, intermediateCurrencyCode, intermediateAmount);
            } else {
                description = String.format("卖出 %.2f %s 可获得 %.2f %s，通过中间币种 %s（%.2f）转换",
                        sourceAmount, sourceCurrencyCode, targetAmount, targetCurrencyCode, intermediateCurrencyCode, intermediateAmount);
            }
            calculationResult.put("description", description);

            log.info("跨币种汇率转换计算成功: {} {} = {} {}, 通过中间币种 {} ({})",
                    sourceAmount, sourceCurrencyCode, targetAmount, targetCurrencyCode, intermediateCurrencyCode, intermediateAmount);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "跨币种汇率转换计算成功", calculationResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("跨币种汇率转换计算失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 跨币种购买功能（使用一种外币购买另一种外币）
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param bankUserId 银行用户ID
     * @param sourceCurrencyCode 源币种代码（用户支付的外币）
     * @param targetCurrencyCode 目标币种代码（用户购买的外币）
     * @param intermediateCurrencyCode 中间币种代码（通常是基准币种，如CNY）
     * @param targetAmount 目标币种金额（要购买的外币金额）
     * @param sourceRateId 源币种汇率表ID
     * @param targetRateId 目标币种汇率表ID
     * @return 购买结果
     */
    @Transaction(name = "BuyCrossCurrency", intent = Transaction.TYPE.SUBMIT)
    public String buyCrossCurrency(final Context ctx, final String userId, final String bankUserId,
                                 final String sourceCurrencyCode, final String targetCurrencyCode,
                                 final String intermediateCurrencyCode, final double targetAmount,
                                 final String sourceRateId, final String targetRateId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证金额是否大于0
            if (targetAmount <= 0) {
                String errorMessage = "购买金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取用户信息
            String userKey = USER_PREFIX + userId;
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 验证用户状态
            if (!user.isStatus()) {
                String errorMessage = String.format("用户ID %s 已被禁用", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取银行用户信息
            String bankUserKey = USER_PREFIX + bankUserId;
            String bankUserJson = stub.getStringState(bankUserKey);
            if (bankUserJson == null || bankUserJson.isEmpty()) {
                String errorMessage = String.format("银行用户ID %s 不存在", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity bankUser = JSON.parseObject(bankUserJson, UserEntity.class);

            // 验证银行用户状态
            if (!bankUser.isStatus()) {
                String errorMessage = String.format("银行用户ID %s 已被禁用", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取源币种汇率表
            String sourceRateKey = EXCHANGE_RATE_PREFIX + sourceRateId;
            String sourceRateJson = stub.getStringState(sourceRateKey);
            if (sourceRateJson == null || sourceRateJson.isEmpty()) {
                String errorMessage = String.format("源币种汇率表ID %s 不存在", sourceRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity sourceRate = JSON.parseObject(sourceRateJson, ExchangeRateEntity.class);

            // 验证源币种汇率表状态
            if (!sourceRate.isStatus()) {
                String errorMessage = String.format("源币种汇率表ID %s 已失效", sourceRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证源币种汇率表的币种与源币种是否匹配
            if (!sourceRate.getCurrencyCode().equals(sourceCurrencyCode)) {
                String errorMessage = String.format("源币种汇率表ID %s 的币种 %s 与源币种 %s 不匹配",
                        sourceRateId, sourceRate.getCurrencyCode(), sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取目标币种汇率表
            String targetRateKey = EXCHANGE_RATE_PREFIX + targetRateId;
            String targetRateJson = stub.getStringState(targetRateKey);
            if (targetRateJson == null || targetRateJson.isEmpty()) {
                String errorMessage = String.format("目标币种汇率表ID %s 不存在", targetRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity targetRate = JSON.parseObject(targetRateJson, ExchangeRateEntity.class);

            // 验证目标币种汇率表状态
            if (!targetRate.isStatus()) {
                String errorMessage = String.format("目标币种汇率表ID %s 已失效", targetRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证目标币种汇率表的币种与目标币种是否匹配
            if (!targetRate.getCurrencyCode().equals(targetCurrencyCode)) {
                String errorMessage = String.format("目标币种汇率表ID %s 的币种 %s 与目标币种 %s 不匹配",
                        targetRateId, targetRate.getCurrencyCode(), targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 计算跨币种转换
            // 1. 目标币种→中间币种（使用银行卖出价）
            double intermediateAmount = targetAmount * targetRate.getBankSellRate();

            // 2. 中间币种→源币种（使用银行买入价的倒数）
            double sourceAmount = intermediateAmount / sourceRate.getBankBuyRate();

            // 验证用户是否支持源币种和目标币种
            boolean userHasSourceCurrency = false;
            int userSourceCurrencyIndex = -1;
            boolean userHasTargetCurrency = false;
            int userTargetCurrencyIndex = -1;

            for (int i = 0; i < user.getCurrency().length; i++) {
                if (user.getCurrency()[i].equals(sourceCurrencyCode)) {
                    userHasSourceCurrency = true;
                    userSourceCurrencyIndex = i;
                }
                if (user.getCurrency()[i].equals(targetCurrencyCode)) {
                    userHasTargetCurrency = true;
                    userTargetCurrencyIndex = i;
                }
            }

            if (!userHasSourceCurrency) {
                String errorMessage = String.format("用户ID %s 没有源币种 %s", userId, sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 如果用户没有目标币种，需要添加该币种
            if (!userHasTargetCurrency) {
                // 添加新币种
                String[] newCurrencies = new String[user.getCurrency().length + 1];
                Double[] newBalances = new Double[user.getBalance().length + 1];

                // 复制原有币种和余额
                for (int i = 0; i < user.getCurrency().length; i++) {
                    newCurrencies[i] = user.getCurrency()[i];
                    newBalances[i] = user.getBalance()[i];
                }

                // 添加新币种
                newCurrencies[user.getCurrency().length] = targetCurrencyCode;
                newBalances[user.getBalance().length] = 0.0; // 初始余额为0

                user.setCurrency(newCurrencies);
                user.setBalance(newBalances);

                // 更新目标币种索引
                userTargetCurrencyIndex = user.getCurrency().length - 1;
            }

            // 验证银行是否支持源币种和目标币种
            boolean bankHasSourceCurrency = false;
            int bankSourceCurrencyIndex = -1;
            boolean bankHasTargetCurrency = false;
            int bankTargetCurrencyIndex = -1;

            for (int i = 0; i < bankUser.getCurrency().length; i++) {
                if (bankUser.getCurrency()[i].equals(sourceCurrencyCode)) {
                    bankHasSourceCurrency = true;
                    bankSourceCurrencyIndex = i;
                }
                if (bankUser.getCurrency()[i].equals(targetCurrencyCode)) {
                    bankHasTargetCurrency = true;
                    bankTargetCurrencyIndex = i;
                }
            }

            if (!bankHasSourceCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有源币种 %s", bankUserId, sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            if (!bankHasTargetCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有目标币种 %s", bankUserId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证用户源币种余额是否足够
            Double userSourceBalance = user.getBalance()[userSourceCurrencyIndex];
            // 处理可能的null值
            if (userSourceBalance == null) {
                userSourceBalance = 0.0;
                user.getBalance()[userSourceCurrencyIndex] = 0.0;
            }
            if (userSourceBalance < sourceAmount) {
                String errorMessage = String.format("用户ID %s 的 %s 余额不足（需要 %.2f %s）", userId, sourceCurrencyCode, sourceAmount, sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证银行目标币种余额是否足够
            Double bankTargetBalance = bankUser.getBalance()[bankTargetCurrencyIndex];
            // 处理可能的null值
            if (bankTargetBalance == null) {
                bankTargetBalance = 0.0;
                bankUser.getBalance()[bankTargetCurrencyIndex] = 0.0;
            }
            if (bankTargetBalance < targetAmount) {
                String errorMessage = String.format("银行用户ID %s 的 %s 余额不足", bankUserId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新用户源币种余额（减少）
            Double[] userBalances = user.getBalance();
            // 处理可能的null值
            if (userBalances[userSourceCurrencyIndex] == null) {
                userBalances[userSourceCurrencyIndex] = 0.0;
            }
            userBalances[userSourceCurrencyIndex] -= sourceAmount;

            // 更新用户目标币种余额（增加）
            if (userBalances[userTargetCurrencyIndex] == null) {
                userBalances[userTargetCurrencyIndex] = 0.0;
            }
            userBalances[userTargetCurrencyIndex] += targetAmount;

            // 更新银行源币种余额（增加）
            Double[] bankBalances = bankUser.getBalance();
            // 处理可能的null值
            if (bankBalances[bankSourceCurrencyIndex] == null) {
                bankBalances[bankSourceCurrencyIndex] = 0.0;
            }
            bankBalances[bankSourceCurrencyIndex] += sourceAmount;

            // 更新银行目标币种余额（减少）
            if (bankBalances[bankTargetCurrencyIndex] == null) {
                bankBalances[bankTargetCurrencyIndex] = 0.0;
            }
            bankBalances[bankTargetCurrencyIndex] -= targetAmount;

            // 创建用户的交易记录
            TransactionRecord userRecord = new TransactionRecord();
            userRecord.setTransactionId(transactionId);
            userRecord.setTransactionType("BUY_CROSS_CURRENCY");
            userRecord.setAmount(targetAmount);
            userRecord.setDescription("跨币种购买: 支付 " + sourceAmount + " " + sourceCurrencyCode + ", 获得 " + targetAmount + " " + targetCurrencyCode + ", 通过中间币种 " + intermediateCurrencyCode + " (" + intermediateAmount + ")");
            userRecord.setCounterpartyId(bankUserId);
            userRecord.setStatus("COMPLETED");
            userRecord.setTimestamp(txTimestamp);

            // 添加交易记录到用户
            TransactionRecord[] userTransactions = user.getTransactionRecords();
            List<TransactionRecord> userTransactionsList = new ArrayList<>();
            if (userTransactions != null) {
                for (TransactionRecord record : userTransactions) {
                    userTransactionsList.add(record);
                }
            }
            userTransactionsList.add(userRecord);
            user.setTransactionRecords(userTransactionsList.toArray(new TransactionRecord[0]));

            // 创建银行的交易记录
            TransactionRecord bankRecord = new TransactionRecord();
            bankRecord.setTransactionId(transactionId);
            bankRecord.setTransactionType("SELL_CROSS_CURRENCY");
            bankRecord.setAmount(targetAmount);
            bankRecord.setDescription("跨币种卖出: 提供 " + targetAmount + " " + targetCurrencyCode + ", 收到 " + sourceAmount + " " + sourceCurrencyCode + ", 通过中间币种 " + intermediateCurrencyCode + " (" + intermediateAmount + ")");
            bankRecord.setCounterpartyId(userId);
            bankRecord.setStatus("COMPLETED");
            bankRecord.setTimestamp(txTimestamp);

            // 添加交易记录到银行
            TransactionRecord[] bankTransactions = bankUser.getTransactionRecords();
            List<TransactionRecord> bankTransactionsList = new ArrayList<>();
            if (bankTransactions != null) {
                for (TransactionRecord record : bankTransactions) {
                    bankTransactionsList.add(record);
                }
            }
            bankTransactionsList.add(bankRecord);
            bankUser.setTransactionRecords(bankTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户和银行对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));
            stub.putStringState(bankUserKey, JSON.toJSONString(bankUser));

            // 构建购买结果
            Map<String, Object> buyResult = new HashMap<>();
            buyResult.put("transactionId", transactionId);
            buyResult.put("userId", userId);
            buyResult.put("bankUserId", bankUserId);
            buyResult.put("sourceCurrency", sourceCurrencyCode);
            buyResult.put("targetCurrency", targetCurrencyCode);
            buyResult.put("intermediateCurrency", intermediateCurrencyCode);
            buyResult.put("sourceAmount", sourceAmount);
            buyResult.put("intermediateAmount", intermediateAmount);
            buyResult.put("targetAmount", targetAmount);
            buyResult.put("sourceRate", sourceRate.getBankBuyRate());
            buyResult.put("targetRate", targetRate.getBankSellRate());
            buyResult.put("timestamp", txTimestamp);
            buyResult.put("status", "COMPLETED");

            stub.setEvent("buy_cross_currency", JSON.toJSONString(buyResult).getBytes(StandardCharsets.UTF_8));

            log.info("跨币种购买成功: 用户 {} 支付 {} {}, 获得 {} {}, 通过中间币种 {} ({})",
                    userId, sourceAmount, sourceCurrencyCode, targetAmount, targetCurrencyCode, intermediateCurrencyCode, intermediateAmount);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "跨币种购买成功", buyResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("跨币种购买失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 用户卖出外汇功能
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param bankUserId 银行用户ID
     * @param targetCurrencyCode 目标币种代码（要卖出的外币）
     * @param baseCurrencyCode 基准币种代码（要换回的本币）
     * @param targetAmount 目标币种金额（要卖出的外币金额）
     * @param rateId 汇率表ID
     * @return 卖出结果
     */
    @Transaction(name = "SellForeignCurrency", intent = Transaction.TYPE.SUBMIT)
    public String sellForeignCurrency(final Context ctx, final String userId, final String bankUserId,
                                    final String targetCurrencyCode, final String baseCurrencyCode,
                                    final double targetAmount, final String rateId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证金额是否大于0
            if (targetAmount <= 0) {
                String errorMessage = "卖出金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取用户信息
            String userKey = USER_PREFIX + userId;
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 验证用户状态
            if (!user.isStatus()) {
                String errorMessage = String.format("用户ID %s 已被禁用", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取银行用户信息
            String bankUserKey = USER_PREFIX + bankUserId;
            String bankUserJson = stub.getStringState(bankUserKey);
            if (bankUserJson == null || bankUserJson.isEmpty()) {
                String errorMessage = String.format("银行用户ID %s 不存在", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity bankUser = JSON.parseObject(bankUserJson, UserEntity.class);

            // 验证银行用户状态
            if (!bankUser.isStatus()) {
                String errorMessage = String.format("银行用户ID %s 已被禁用", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取汇率表信息
            String rateKey = EXCHANGE_RATE_PREFIX + rateId;
            String rateJson = stub.getStringState(rateKey);
            if (rateJson == null || rateJson.isEmpty()) {
                String errorMessage = String.format("汇率表ID %s 不存在", rateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity exchangeRate = JSON.parseObject(rateJson, ExchangeRateEntity.class);

            // 验证汇率表状态
            if (!exchangeRate.isStatus()) {
                String errorMessage = String.format("汇率表ID %s 已失效", rateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证汇率表的币种与目标币种是否匹配
            if (!exchangeRate.getCurrencyCode().equals(targetCurrencyCode)) {
                String errorMessage = String.format("汇率表ID %s 的币种 %s 与目标币种 %s 不匹配",
                        rateId, exchangeRate.getCurrencyCode(), targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证用户是否支持基准币种和目标币种
            boolean userHasBaseCurrency = false;
            int userBaseCurrencyIndex = -1;
            boolean userHasTargetCurrency = false;
            int userTargetCurrencyIndex = -1;

            for (int i = 0; i < user.getCurrency().length; i++) {
                if (user.getCurrency()[i].equals(baseCurrencyCode)) {
                    userHasBaseCurrency = true;
                    userBaseCurrencyIndex = i;
                }
                if (user.getCurrency()[i].equals(targetCurrencyCode)) {
                    userHasTargetCurrency = true;
                    userTargetCurrencyIndex = i;
                }
            }

            if (!userHasBaseCurrency) {
                // 如果用户没有基准币种，需要添加该币种
                String[] newCurrencies = new String[user.getCurrency().length + 1];
                Double[] newBalances = new Double[user.getBalance().length + 1];

                // 复制原有币种和余额
                for (int i = 0; i < user.getCurrency().length; i++) {
                    newCurrencies[i] = user.getCurrency()[i];
                    newBalances[i] = user.getBalance()[i];
                }

                // 添加新币种
                newCurrencies[user.getCurrency().length] = baseCurrencyCode;
                newBalances[user.getBalance().length] = 0.0; // 初始余额为0

                user.setCurrency(newCurrencies);
                user.setBalance(newBalances);

                // 更新基准币种索引
                userBaseCurrencyIndex = user.getCurrency().length - 1;
            }

            if (!userHasTargetCurrency) {
                String errorMessage = String.format("用户ID %s 没有目标币种 %s", userId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证银行是否支持基准币种和目标币种
            boolean bankHasBaseCurrency = false;
            int bankBaseCurrencyIndex = -1;
            boolean bankHasTargetCurrency = false;
            int bankTargetCurrencyIndex = -1;

            for (int i = 0; i < bankUser.getCurrency().length; i++) {
                if (bankUser.getCurrency()[i].equals(baseCurrencyCode)) {
                    bankHasBaseCurrency = true;
                    bankBaseCurrencyIndex = i;
                }
                if (bankUser.getCurrency()[i].equals(targetCurrencyCode)) {
                    bankHasTargetCurrency = true;
                    bankTargetCurrencyIndex = i;
                }
            }

            if (!bankHasBaseCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有基准币种 %s", bankUserId, baseCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            if (!bankHasTargetCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有目标币种 %s", bankUserId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证用户目标币种余额是否足够
            Double userTargetBalance = user.getBalance()[userTargetCurrencyIndex];
            // 处理可能的null值
            if (userTargetBalance == null) {
                userTargetBalance = 0.0;
                user.getBalance()[userTargetCurrencyIndex] = 0.0;
            }
            if (userTargetBalance < targetAmount) {
                String errorMessage = String.format("用户ID %s 的 %s 余额不足", userId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 计算可以获得的基准币种金额（使用银行买入价）
            double baseAmount = targetAmount * exchangeRate.getBankBuyRate();

            // 验证银行基准币种余额是否足够
            Double bankBaseBalance = bankUser.getBalance()[bankBaseCurrencyIndex];
            // 处理可能的null值
            if (bankBaseBalance == null) {
                bankBaseBalance = 0.0;
                bankUser.getBalance()[bankBaseCurrencyIndex] = 0.0;
            }
            if (bankBaseBalance < baseAmount) {
                String errorMessage = String.format("银行用户ID %s 的 %s 余额不足", bankUserId, baseCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新用户目标币种余额（减少）
            Double[] userBalances = user.getBalance();
            // 处理可能的null值
            if (userBalances[userTargetCurrencyIndex] == null) {
                userBalances[userTargetCurrencyIndex] = 0.0;
            }
            userBalances[userTargetCurrencyIndex] -= targetAmount;

            // 更新用户基准币种余额（增加）
            if (userBalances[userBaseCurrencyIndex] == null) {
                userBalances[userBaseCurrencyIndex] = 0.0;
            }
            userBalances[userBaseCurrencyIndex] += baseAmount;

            // 更新银行目标币种余额（增加）
            Double[] bankBalances = bankUser.getBalance();
            // 处理可能的null值
            if (bankBalances[bankTargetCurrencyIndex] == null) {
                bankBalances[bankTargetCurrencyIndex] = 0.0;
            }
            bankBalances[bankTargetCurrencyIndex] += targetAmount;

            // 更新银行基准币种余额（减少）
            if (bankBalances[bankBaseCurrencyIndex] == null) {
                bankBalances[bankBaseCurrencyIndex] = 0.0;
            }
            bankBalances[bankBaseCurrencyIndex] -= baseAmount;

            // 创建用户的交易记录
            TransactionRecord userRecord = new TransactionRecord();
            userRecord.setTransactionId(transactionId);
            userRecord.setTransactionType("SELL_FOREIGN_CURRENCY");
            userRecord.setAmount(targetAmount);
            userRecord.setDescription("卖出外汇: 支付 " + targetAmount + " " + targetCurrencyCode + ", 获得 " + baseAmount + " " + baseCurrencyCode + ", 汇率: " + exchangeRate.getBankBuyRate());
            userRecord.setCounterpartyId(bankUserId);
            userRecord.setStatus("COMPLETED");
            userRecord.setTimestamp(txTimestamp);

            // 添加交易记录到用户
            TransactionRecord[] userTransactions = user.getTransactionRecords();
            List<TransactionRecord> userTransactionsList = new ArrayList<>();
            if (userTransactions != null) {
                for (TransactionRecord record : userTransactions) {
                    userTransactionsList.add(record);
                }
            }
            userTransactionsList.add(userRecord);
            user.setTransactionRecords(userTransactionsList.toArray(new TransactionRecord[0]));

            // 创建银行的交易记录
            TransactionRecord bankRecord = new TransactionRecord();
            bankRecord.setTransactionId(transactionId);
            bankRecord.setTransactionType("BUY_FOREIGN_CURRENCY");
            bankRecord.setAmount(targetAmount);
            bankRecord.setDescription("买入外汇: 提供 " + baseAmount + " " + baseCurrencyCode + ", 收到 " + targetAmount + " " + targetCurrencyCode + ", 汇率: " + exchangeRate.getBankBuyRate());
            bankRecord.setCounterpartyId(userId);
            bankRecord.setStatus("COMPLETED");
            bankRecord.setTimestamp(txTimestamp);

            // 添加交易记录到银行
            TransactionRecord[] bankTransactions = bankUser.getTransactionRecords();
            List<TransactionRecord> bankTransactionsList = new ArrayList<>();
            if (bankTransactions != null) {
                for (TransactionRecord record : bankTransactions) {
                    bankTransactionsList.add(record);
                }
            }
            bankTransactionsList.add(bankRecord);
            bankUser.setTransactionRecords(bankTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户和银行对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));
            stub.putStringState(bankUserKey, JSON.toJSONString(bankUser));

            // 构建卖出结果
            Map<String, Object> sellResult = new HashMap<>();
            sellResult.put("transactionId", transactionId);
            sellResult.put("userId", userId);
            sellResult.put("bankUserId", bankUserId);
            sellResult.put("targetCurrency", targetCurrencyCode);
            sellResult.put("baseCurrency", baseCurrencyCode);
            sellResult.put("targetAmount", targetAmount);
            sellResult.put("baseAmount", baseAmount);
            sellResult.put("exchangeRate", exchangeRate.getBankBuyRate());
            sellResult.put("timestamp", txTimestamp);
            sellResult.put("status", "COMPLETED");

            stub.setEvent("sell_foreign_currency", JSON.toJSONString(sellResult).getBytes(StandardCharsets.UTF_8));

            log.info("卖出外汇成功: 用户 {} 支付 {} {}, 获得 {} {}, 汇率: {}",
                    userId, targetAmount, targetCurrencyCode, baseAmount, baseCurrencyCode, exchangeRate.getBankBuyRate());
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "卖出外汇成功", sellResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("卖出外汇失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }

    /**
     * 跨币种卖出功能（将一种外币卖出并获得另一种外币）
     *
     * @param ctx 交易上下文
     * @param userId 用户ID
     * @param bankUserId 银行用户ID
     * @param sourceCurrencyCode 源币种代码（用户卖出的外币）
     * @param targetCurrencyCode 目标币种代码（用户获得的外币）
     * @param intermediateCurrencyCode 中间币种代码（通常是基准币种，如CNY）
     * @param sourceAmount 源币种金额（要卖出的外币金额）
     * @param sourceRateId 源币种汇率表ID
     * @param targetRateId 目标币种汇率表ID
     * @return 卖出结果
     */
    @Transaction(name = "SellCrossCurrency", intent = Transaction.TYPE.SUBMIT)
    public String sellCrossCurrency(final Context ctx, final String userId, final String bankUserId,
                                  final String sourceCurrencyCode, final String targetCurrencyCode,
                                  final String intermediateCurrencyCode, final double sourceAmount,
                                  final String sourceRateId, final String targetRateId) {
        ChaincodeStub stub = ctx.getStub();
        long txTimestamp = stub.getTxTimestamp().toEpochMilli();

        try {
            // 验证金额是否大于0
            if (sourceAmount <= 0) {
                String errorMessage = "卖出金额必须大于0";
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取用户信息
            String userKey = USER_PREFIX + userId;
            String userJson = stub.getStringState(userKey);
            if (userJson == null || userJson.isEmpty()) {
                String errorMessage = String.format("用户ID %s 不存在", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);

            // 验证用户状态
            if (!user.isStatus()) {
                String errorMessage = String.format("用户ID %s 已被禁用", userId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取银行用户信息
            String bankUserKey = USER_PREFIX + bankUserId;
            String bankUserJson = stub.getStringState(bankUserKey);
            if (bankUserJson == null || bankUserJson.isEmpty()) {
                String errorMessage = String.format("银行用户ID %s 不存在", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            UserEntity bankUser = JSON.parseObject(bankUserJson, UserEntity.class);

            // 验证银行用户状态
            if (!bankUser.isStatus()) {
                String errorMessage = String.format("银行用户ID %s 已被禁用", bankUserId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取源币种汇率表
            String sourceRateKey = EXCHANGE_RATE_PREFIX + sourceRateId;
            String sourceRateJson = stub.getStringState(sourceRateKey);
            if (sourceRateJson == null || sourceRateJson.isEmpty()) {
                String errorMessage = String.format("源币种汇率表ID %s 不存在", sourceRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity sourceRate = JSON.parseObject(sourceRateJson, ExchangeRateEntity.class);

            // 验证源币种汇率表状态
            if (!sourceRate.isStatus()) {
                String errorMessage = String.format("源币种汇率表ID %s 已失效", sourceRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证源币种汇率表的币种与源币种是否匹配
            if (!sourceRate.getCurrencyCode().equals(sourceCurrencyCode)) {
                String errorMessage = String.format("源币种汇率表ID %s 的币种 %s 与源币种 %s 不匹配",
                        sourceRateId, sourceRate.getCurrencyCode(), sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 获取目标币种汇率表
            String targetRateKey = EXCHANGE_RATE_PREFIX + targetRateId;
            String targetRateJson = stub.getStringState(targetRateKey);
            if (targetRateJson == null || targetRateJson.isEmpty()) {
                String errorMessage = String.format("目标币种汇率表ID %s 不存在", targetRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }
            ExchangeRateEntity targetRate = JSON.parseObject(targetRateJson, ExchangeRateEntity.class);

            // 验证目标币种汇率表状态
            if (!targetRate.isStatus()) {
                String errorMessage = String.format("目标币种汇率表ID %s 已失效", targetRateId);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证目标币种汇率表的币种与目标币种是否匹配
            if (!targetRate.getCurrencyCode().equals(targetCurrencyCode)) {
                String errorMessage = String.format("目标币种汇率表ID %s 的币种 %s 与目标币种 %s 不匹配",
                        targetRateId, targetRate.getCurrencyCode(), targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 计算跨币种转换
            // 1. 源币种→中间币种（使用银行买入价）
            double intermediateAmount = sourceAmount * sourceRate.getBankBuyRate();

            // 2. 中间币种→目标币种（使用银行卖出价的倒数）
            double targetAmount = intermediateAmount / targetRate.getBankSellRate();

            // 验证用户是否支持源币种和目标币种
            boolean userHasSourceCurrency = false;
            int userSourceCurrencyIndex = -1;
            boolean userHasTargetCurrency = false;
            int userTargetCurrencyIndex = -1;

            for (int i = 0; i < user.getCurrency().length; i++) {
                if (user.getCurrency()[i].equals(sourceCurrencyCode)) {
                    userHasSourceCurrency = true;
                    userSourceCurrencyIndex = i;
                }
                if (user.getCurrency()[i].equals(targetCurrencyCode)) {
                    userHasTargetCurrency = true;
                    userTargetCurrencyIndex = i;
                }
            }

            if (!userHasSourceCurrency) {
                String errorMessage = String.format("用户ID %s 没有源币种 %s", userId, sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 如果用户没有目标币种，需要添加该币种
            if (!userHasTargetCurrency) {
                // 添加新币种
                String[] newCurrencies = new String[user.getCurrency().length + 1];
                Double[] newBalances = new Double[user.getBalance().length + 1];

                // 复制原有币种和余额
                for (int i = 0; i < user.getCurrency().length; i++) {
                    newCurrencies[i] = user.getCurrency()[i];
                    newBalances[i] = user.getBalance()[i];
                }

                // 添加新币种
                newCurrencies[user.getCurrency().length] = targetCurrencyCode;
                newBalances[user.getBalance().length] = 0.0; // 初始余额为0

                user.setCurrency(newCurrencies);
                user.setBalance(newBalances);

                // 更新目标币种索引
                userTargetCurrencyIndex = user.getCurrency().length - 1;
            }

            // 验证银行是否支持源币种和目标币种
            boolean bankHasSourceCurrency = false;
            int bankSourceCurrencyIndex = -1;
            boolean bankHasTargetCurrency = false;
            int bankTargetCurrencyIndex = -1;

            for (int i = 0; i < bankUser.getCurrency().length; i++) {
                if (bankUser.getCurrency()[i].equals(sourceCurrencyCode)) {
                    bankHasSourceCurrency = true;
                    bankSourceCurrencyIndex = i;
                }
                if (bankUser.getCurrency()[i].equals(targetCurrencyCode)) {
                    bankHasTargetCurrency = true;
                    bankTargetCurrencyIndex = i;
                }
            }

            if (!bankHasSourceCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有源币种 %s", bankUserId, sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            if (!bankHasTargetCurrency) {
                String errorMessage = String.format("银行用户ID %s 没有目标币种 %s", bankUserId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证用户源币种余额是否足够
            Double userSourceBalance = user.getBalance()[userSourceCurrencyIndex];
            // 处理可能的null值
            if (userSourceBalance == null) {
                userSourceBalance = 0.0;
                user.getBalance()[userSourceCurrencyIndex] = 0.0;
            }
            if (userSourceBalance < sourceAmount) {
                String errorMessage = String.format("用户ID %s 的 %s 余额不足", userId, sourceCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 验证银行目标币种余额是否足够
            Double bankTargetBalance = bankUser.getBalance()[bankTargetCurrencyIndex];
            // 处理可能的null值
            if (bankTargetBalance == null) {
                bankTargetBalance = 0.0;
                bankUser.getBalance()[bankTargetCurrencyIndex] = 0.0;
            }
            if (bankTargetBalance < targetAmount) {
                String errorMessage = String.format("银行用户ID %s 的 %s 余额不足", bankUserId, targetCurrencyCode);
                log.error(errorMessage);
                throw new ChaincodeException(errorMessage);
            }

            // 生成交易ID
            String transactionId = stub.getTxId();

            // 更新用户源币种余额（减少）
            Double[] userBalances = user.getBalance();
            // 处理可能的null值
            if (userBalances[userSourceCurrencyIndex] == null) {
                userBalances[userSourceCurrencyIndex] = 0.0;
            }
            userBalances[userSourceCurrencyIndex] -= sourceAmount;

            // 更新用户目标币种余额（增加）
            if (userBalances[userTargetCurrencyIndex] == null) {
                userBalances[userTargetCurrencyIndex] = 0.0;
            }
            userBalances[userTargetCurrencyIndex] += targetAmount;

            // 更新银行源币种余额（增加）
            Double[] bankBalances = bankUser.getBalance();
            // 处理可能的null值
            if (bankBalances[bankSourceCurrencyIndex] == null) {
                bankBalances[bankSourceCurrencyIndex] = 0.0;
            }
            bankBalances[bankSourceCurrencyIndex] += sourceAmount;

            // 更新银行目标币种余额（减少）
            if (bankBalances[bankTargetCurrencyIndex] == null) {
                bankBalances[bankTargetCurrencyIndex] = 0.0;
            }
            bankBalances[bankTargetCurrencyIndex] -= targetAmount;

            // 创建用户的交易记录
            TransactionRecord userRecord = new TransactionRecord();
            userRecord.setTransactionId(transactionId);
            userRecord.setTransactionType("SELL_CROSS_CURRENCY");
            userRecord.setAmount(sourceAmount);
            userRecord.setDescription("跨币种卖出: 支付 " + sourceAmount + " " + sourceCurrencyCode + ", 获得 " + targetAmount + " " + targetCurrencyCode + ", 通过中间币种 " + intermediateCurrencyCode + " (" + intermediateAmount + ")");
            userRecord.setCounterpartyId(bankUserId);
            userRecord.setStatus("COMPLETED");
            userRecord.setTimestamp(txTimestamp);

            // 添加交易记录到用户
            TransactionRecord[] userTransactions = user.getTransactionRecords();
            List<TransactionRecord> userTransactionsList = new ArrayList<>();
            if (userTransactions != null) {
                for (TransactionRecord record : userTransactions) {
                    userTransactionsList.add(record);
                }
            }
            userTransactionsList.add(userRecord);
            user.setTransactionRecords(userTransactionsList.toArray(new TransactionRecord[0]));

            // 创建银行的交易记录
            TransactionRecord bankRecord = new TransactionRecord();
            bankRecord.setTransactionId(transactionId);
            bankRecord.setTransactionType("BUY_CROSS_CURRENCY");
            bankRecord.setAmount(sourceAmount);
            bankRecord.setDescription("跨币种买入: 提供 " + targetAmount + " " + targetCurrencyCode + ", 收到 " + sourceAmount + " " + sourceCurrencyCode + ", 通过中间币种 " + intermediateCurrencyCode + " (" + intermediateAmount + ")");
            bankRecord.setCounterpartyId(userId);
            bankRecord.setStatus("COMPLETED");
            bankRecord.setTimestamp(txTimestamp);

            // 添加交易记录到银行
            TransactionRecord[] bankTransactions = bankUser.getTransactionRecords();
            List<TransactionRecord> bankTransactionsList = new ArrayList<>();
            if (bankTransactions != null) {
                for (TransactionRecord record : bankTransactions) {
                    bankTransactionsList.add(record);
                }
            }
            bankTransactionsList.add(bankRecord);
            bankUser.setTransactionRecords(bankTransactionsList.toArray(new TransactionRecord[0]));

            // 将更新后的用户和银行对象序列化为JSON并存储到账本
            stub.putStringState(userKey, JSON.toJSONString(user));
            stub.putStringState(bankUserKey, JSON.toJSONString(bankUser));

            // 构建卖出结果
            Map<String, Object> sellResult = new HashMap<>();
            sellResult.put("transactionId", transactionId);
            sellResult.put("userId", userId);
            sellResult.put("bankUserId", bankUserId);
            sellResult.put("sourceCurrency", sourceCurrencyCode);
            sellResult.put("targetCurrency", targetCurrencyCode);
            sellResult.put("intermediateCurrency", intermediateCurrencyCode);
            sellResult.put("sourceAmount", sourceAmount);
            sellResult.put("intermediateAmount", intermediateAmount);
            sellResult.put("targetAmount", targetAmount);
            sellResult.put("sourceRate", sourceRate.getBankBuyRate());
            sellResult.put("targetRate", targetRate.getBankSellRate());
            sellResult.put("timestamp", txTimestamp);
            sellResult.put("status", "COMPLETED");

            stub.setEvent("sell_cross_currency", JSON.toJSONString(sellResult).getBytes(StandardCharsets.UTF_8));

            log.info("跨币种卖出成功: 用户 {} 支付 {} {}, 获得 {} {}, 通过中间币种 {} ({})",
                    userId, sourceAmount, sourceCurrencyCode, targetAmount, targetCurrencyCode, intermediateCurrencyCode, intermediateAmount);
            return JSON.toJSONString(new ResultUtil<>(SUCCESS_CODE, "跨币种卖出成功", sellResult, txTimestamp));
        } catch (Exception e) {
            String errorMessage = String.format("跨币种卖出失败: %s", e.getMessage());
            log.error(errorMessage);
            throw new ChaincodeException(errorMessage);
        }
    }
}
