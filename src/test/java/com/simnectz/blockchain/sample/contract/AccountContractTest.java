package com.simnectz.blockchain.sample.contract;

import com.alibaba.fastjson.JSON;
import com.simnectz.blockchain.sample.entity.UserEntity;
import com.simnectz.blockchain.sample.utils.ResultUtil;
import org.hyperledger.fabric.contract.Context;
import org.hyperledger.fabric.shim.ChaincodeStub;
import org.hyperledger.fabric.shim.ledger.KeyValue;
import org.hyperledger.fabric.shim.ledger.QueryResultsIterator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AccountContract 测试类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
public class AccountContractTest {

    @Mock
    private Context ctx;

    @Mock
    private ChaincodeStub stub;

    @Mock
    private QueryResultsIterator<KeyValue> queryResultsIterator;

    private AccountContract contract;

    @BeforeEach
    void setUp() {
        contract = new AccountContract();
        when(ctx.getStub()).thenReturn(stub);
        when(stub.getTxTimestamp()).thenReturn(Instant.ofEpochMilli(1640995200000L)); // 2022-01-01 00:00:00
        when(stub.getTxId()).thenReturn("test-tx-id-123");
    }

    /**
     * 测试用户注册成功的情况
     */
    @Test
    void testRegisterUser_Success() {
        // 准备测试数据
        String userId = "testuser001";
        String password = "password123";
        String accountType = "SAVING";
        String currency = "CNY,USD";
        String identity = "USER";

        // Mock 用户不存在的情况
        when(stub.getStringState("USER_" + userId)).thenReturn("");

        // 执行测试
        String result = contract.registerUser(ctx, userId, password, accountType, currency, identity);

        // 验证结果
        assertNotNull(result);
        
        // 解析返回的JSON结果
        ResultUtil<?> resultUtil = JSON.parseObject(result, ResultUtil.class);
        assertEquals(20000, resultUtil.getCode());
        assertEquals("用户创建成功", resultUtil.getMessage());
        assertEquals(1640995200000L, resultUtil.getTimestamp());
        assertEquals("test-tx-id-123", resultUtil.getTransactionId());

        // 验证调用了putStringState方法
        verify(stub, times(1)).putStringState(eq("USER_" + userId), anyString());
        
        // 验证存储的用户数据
        verify(stub).putStringState(eq("USER_" + userId), argThat(userJson -> {
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);
            return userId.equals(user.getUserId()) &&
                   password.equals(user.getPassword()) &&
                   accountType.equals(user.getAccountType()) &&
                   identity.equals(user.getIdentity()) &&
                   user.getCardId().length() == 16 &&
                   user.getCardId().startsWith("622848") &&
                   user.isStatus() &&
                   user.getCreatedTimestamp() == 1640995200000L &&
                   user.getCurrency().length == 2 &&
                   "CNY".equals(user.getCurrency()[0]) &&
                   "USD".equals(user.getCurrency()[1]) &&
                   user.getBalance().length == 1 &&
                   user.getBalance()[0] == 0.0 &&
                   user.getTransactionRecords().length == 0;
        }));
    }

    /**
     * 测试用户注册时使用默认参数的情况
     */
    @Test
    void testRegisterUser_WithDefaultValues() {
        // 准备测试数据
        String userId = "testuser002";
        String password = "password123";
        String accountType = null; // 测试默认值
        String currency = "EUR";
        String identity = null; // 测试默认值

        // Mock 用户不存在的情况
        when(stub.getStringState("USER_" + userId)).thenReturn("");

        // 执行测试
        String result = contract.registerUser(ctx, userId, password, accountType, currency, identity);

        // 验证结果
        assertNotNull(result);
        
        ResultUtil<?> resultUtil = JSON.parseObject(result, ResultUtil.class);
        assertEquals(20000, resultUtil.getCode());

        // 验证存储的用户数据使用了默认值
        verify(stub).putStringState(eq("USER_" + userId), argThat(userJson -> {
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);
            return "SAVING".equals(user.getAccountType()) && // 默认账户类型
                   "USER".equals(user.getIdentity()) && // 默认身份
                   "EUR".equals(user.getCurrency()[0]);
        }));
    }

    /**
     * 测试用户ID已存在的情况
     */
    @Test
    void testRegisterUser_UserAlreadyExists() {
        // 准备测试数据
        String userId = "existinguser";
        String password = "password123";
        String accountType = "SAVING";
        String currency = "CNY";
        String identity = "USER";

        // Mock 用户已存在的情况
        UserEntity existingUser = new UserEntity();
        existingUser.setUserId(userId);
        when(stub.getStringState("USER_" + userId)).thenReturn(JSON.toJSONString(existingUser));

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            contract.registerUser(ctx, userId, password, accountType, currency, identity);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("用户注册失败"));
        assertTrue(exception.getMessage().contains("用户ID " + userId + " 已存在"));

        // 验证没有调用putStringState方法
        verify(stub, never()).putStringState(anyString(), anyString());
    }

    /**
     * 测试多币种注册
     */
    @Test
    void testRegisterUser_MultipleCurrencies() {
        // 准备测试数据
        String userId = "multicurrency";
        String password = "password123";
        String accountType = "FEX";
        String currency = "CNY,USD,EUR,JPY";
        String identity = "BANK";

        // Mock 用户不存在的情况
        when(stub.getStringState("USER_" + userId)).thenReturn("");

        // 执行测试
        String result = contract.registerUser(ctx, userId, password, accountType, currency, identity);

        // 验证结果
        assertNotNull(result);
        
        ResultUtil<?> resultUtil = JSON.parseObject(result, ResultUtil.class);
        assertEquals(20000, resultUtil.getCode());

        // 验证存储的用户数据
        verify(stub).putStringState(eq("USER_" + userId), argThat(userJson -> {
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);
            return user.getCurrency().length == 4 &&
                   "CNY".equals(user.getCurrency()[0]) &&
                   "USD".equals(user.getCurrency()[1]) &&
                   "EUR".equals(user.getCurrency()[2]) &&
                   "JPY".equals(user.getCurrency()[3]) &&
                   "FEX".equals(user.getAccountType()) &&
                   "BANK".equals(user.getIdentity());
        }));
    }

    /**
     * 测试卡号生成的唯一性
     */
    @Test
    void testRegisterUser_CardIdGeneration() {
        // 准备测试数据
        String userId1 = "user001";
        String userId2 = "user002";
        String password = "password123";
        String accountType = "SAVING";
        String currency = "CNY";
        String identity = "USER";

        // Mock 用户不存在的情况
        when(stub.getStringState(anyString())).thenReturn("");

        // 执行第一次注册
        contract.registerUser(ctx, userId1, password, accountType, currency, identity);
        
        // 修改时间戳模拟不同的注册时间
        when(stub.getTxTimestamp()).thenReturn(Instant.ofEpochMilli(1640995260000L)); // 1分钟后
        
        // 执行第二次注册
        contract.registerUser(ctx, userId2, password, accountType, currency, identity);

        // 验证两次调用都成功
        verify(stub, times(2)).putStringState(anyString(), anyString());
        
        // 验证卡号格式正确（16位，以622848开头）
        verify(stub, times(2)).putStringState(anyString(), argThat(userJson -> {
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);
            String cardId = user.getCardId();
            return cardId.length() == 16 && cardId.startsWith("622848");
        }));
    }

    /**
     * 测试空币种参数的处理
     */
    @Test
    void testRegisterUser_EmptyCurrency() {
        // 准备测试数据
        String userId = "testuser003";
        String password = "password123";
        String accountType = "SAVING";
        String currency = ""; // 空币种
        String identity = "USER";

        // Mock 用户不存在的情况
        when(stub.getStringState("USER_" + userId)).thenReturn("");

        // 执行测试
        String result = contract.registerUser(ctx, userId, password, accountType, currency, identity);

        // 验证结果
        assertNotNull(result);
        
        ResultUtil<?> resultUtil = JSON.parseObject(result, ResultUtil.class);
        assertEquals(20000, resultUtil.getCode());

        // 验证存储的用户数据处理了空币种
        verify(stub).putStringState(eq("USER_" + userId), argThat(userJson -> {
            UserEntity user = JSON.parseObject(userJson, UserEntity.class);
            // 空字符串split后会产生一个空字符串元素
            return user.getCurrency().length == 1 && "".equals(user.getCurrency()[0]);
        }));
    }

    /**
     * 测试异常情况下的错误处理
     */
    @Test
    void testRegisterUser_ExceptionHandling() {
        // 准备测试数据
        String userId = "testuser004";
        String password = "password123";
        String accountType = "SAVING";
        String currency = "CNY";
        String identity = "USER";

        // Mock 用户不存在的情况
        when(stub.getStringState("USER_" + userId)).thenReturn("");
        
        // Mock putStringState抛出异常
        doThrow(new RuntimeException("模拟存储异常")).when(stub).putStringState(anyString(), anyString());

        // 执行测试并验证异常
        Exception exception = assertThrows(RuntimeException.class, () -> {
            contract.registerUser(ctx, userId, password, accountType, currency, identity);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("用户注册失败"));
        assertTrue(exception.getMessage().contains("模拟存储异常"));
    }
}
