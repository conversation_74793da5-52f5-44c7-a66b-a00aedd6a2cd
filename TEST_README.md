# AccountContract 测试说明

## 概述

本文档描述了为 `AccountContract` 类中的 `registerUser` 方法编写的单元测试。

## 测试文件

- **测试类**: `src/test/java/com/simnectz/blockchain/sample/contract/AccountContractTest.java`
- **测试方法**: 针对 `registerUser` 方法的多个测试场景

## 测试场景

### 1. 正常注册测试 (`testRegisterUser_Success`)
- **目的**: 测试用户正常注册流程
- **测试数据**: 
  - 用户ID: `testuser001`
  - 密码: `password123`
  - 账户类型: `SAVING`
  - 币种: `CNY,USD`
  - 身份: `USER`
- **验证点**:
  - 返回成功状态码 (20000)
  - 用户数据正确存储
  - 卡号格式正确 (16位，以622848开头)
  - 币种数组正确解析
  - 初始余额为0

### 2. 默认值测试 (`testRegisterUser_WithDefaultValues`)
- **目的**: 测试当账户类型和身份为空时使用默认值
- **验证点**:
  - 账户类型默认为 `SAVING`
  - 身份默认为 `USER`

### 3. 用户已存在测试 (`testRegisterUser_UserAlreadyExists`)
- **目的**: 测试用户ID已存在时的异常处理
- **验证点**:
  - 抛出包含正确错误信息的异常
  - 不执行存储操作

### 4. 多币种测试 (`testRegisterUser_MultipleCurrencies`)
- **目的**: 测试多币种注册功能
- **测试数据**: 币种 `CNY,USD,EUR,JPY`
- **验证点**:
  - 正确解析多个币种
  - 币种数组长度正确

### 5. 卡号生成测试 (`testRegisterUser_CardIdGeneration`)
- **目的**: 测试卡号生成的格式和唯一性
- **验证点**:
  - 卡号长度为16位
  - 卡号以622848开头
  - 不同时间注册的用户卡号不同

### 6. 空币种处理测试 (`testRegisterUser_EmptyCurrency`)
- **目的**: 测试空币种参数的处理
- **验证点**:
  - 正确处理空字符串币种参数

### 7. 异常处理测试 (`testRegisterUser_ExceptionHandling`)
- **目的**: 测试存储异常时的错误处理
- **验证点**:
  - 正确捕获和包装异常
  - 返回包含原始错误信息的异常

## 运行测试

### 使用 Maven 命令
```bash
# 运行所有测试
mvn test

# 只运行 AccountContractTest
mvn test -Dtest=AccountContractTest

# 运行特定测试方法
mvn test -Dtest=AccountContractTest#testRegisterUser_Success
```

### 使用脚本
```bash
# Linux/Mac
./run-tests.sh

# Windows
run-tests.bat
```

## 测试框架和依赖

- **JUnit 5**: 测试框架
- **Mockito**: 模拟框架，用于模拟 Hyperledger Fabric 的 Context 和 ChaincodeStub
- **FastJSON**: JSON 序列化/反序列化

## 模拟对象

测试中使用了以下模拟对象：
- `Context ctx`: 交易上下文
- `ChaincodeStub stub`: 链码存根
- `QueryResultsIterator<KeyValue> queryResultsIterator`: 查询结果迭代器

## 测试数据验证

测试使用了 Mockito 的 `argThat` 方法来验证存储到区块链的用户数据是否正确，包括：
- 用户基本信息 (ID, 密码, 账户类型, 身份)
- 卡号格式和长度
- 币种数组
- 余额数组
- 交易记录数组
- 时间戳和状态

## 注意事项

1. 测试使用固定的时间戳 `1640995200000L` (2022-01-01 00:00:00) 来确保测试结果的一致性
2. 测试使用固定的交易ID `test-tx-id-123` 
3. 卡号生成包含随机数，但测试验证了格式的正确性
4. 测试覆盖了正常流程和异常情况，确保代码的健壮性

## 扩展测试

如需添加更多测试场景，可以考虑：
- 测试不同的账户类型 (CURRENT, FEX)
- 测试不同的身份类型 (BANK)
- 测试更多的异常情况
- 测试并发注册场景
- 测试性能相关的场景
